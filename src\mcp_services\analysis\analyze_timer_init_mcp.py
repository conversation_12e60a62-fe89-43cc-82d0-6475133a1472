
# src/mcp_services/analysis/analyze_timer_init_mcp.py
import configparser
from pathlib import Path
from typing import Dict, Any, List

from ..base_mcp import BaseMCP

class AnalyzeTimerInitMCP(BaseMCP):
    """Timer配置分析MCP服务"""

    def __init__(self):
        super().__init__("analyze_timer_init_mcp")

    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        project_path = Path(input_data["project_path"])
        ioc_file = self._find_ioc_file(project_path)
        
        timer_configs = self._parse_ioc_for_timer(ioc_file)
        
        return {
            "timer_configurations": timer_configs
        }

    def _find_ioc_file(self, project_path: Path) -> Path:
        ioc_files = list(project_path.glob("**/*.ioc"))
        if not ioc_files:
            raise ValueError("未找到STM32CubeMX配置文件(.ioc)")
        return ioc_files[0]

    def _parse_ioc_for_timer(self, ioc_file: Path) -> List[Dict[str, Any]]:
        config = configparser.ConfigParser()
        config.read(ioc_file, encoding='utf-8')
        
        timer_instances = [s for s in config.sections() if s.startswith('TIM')]
        configs = []

        for instance in timer_instances:
            configs.append({
                "instance": instance,
                "prescaler": int(config.get(instance, 'Prescaler', fallback='0')),
                "period": int(config.get(instance, 'Period', fallback='65535')),
            })
        return configs

    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "project_path": {"type": "string"}
            },
            "required": ["project_path"]
        }

    def get_output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "timer_configurations": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "instance": {"type": "string"},
                            "prescaler": {"type": "integer"},
                            "period": {"type": "integer"}
                        }
                    }
                }
            }
        }
