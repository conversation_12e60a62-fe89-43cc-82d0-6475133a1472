# STM32到NXP MCU代码迁移工具

一个基于MCP（Model Context Protocol）架构的智能代码迁移工具，能够自动分析STM32项目并生成功能对等的NXP MCU代码。

## 🚀 项目特性

- **智能分析**：深度分析STM32CubeMX项目配置
- **自动生成**：生成高质量的NXP MCUXpresso SDK代码
- **质量保证**：内置MISRA C合规性检查和代码质量门禁
- **模块化设计**：基于MCP协议的微服务架构
- **多平台支持**：支持多种NXP MCU系列（MCXN、LPC、Kinetis、i.MX RT）
- **VSCode集成**：支持在VSCode等MCP客户端中使用

## 📋 系统要求

- Python 3.8+
- 支持的操作系统：Windows、Linux、macOS

## 🛠️ 安装

### 1. 克隆项目
```bash
git clone https://github.com/your-repo/stm32-to-nxp-migration-tool.git
cd stm32-to-nxp-migration-tool
```

### 2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

## 🎯 快速开始

### 1. 分析STM32项目引脚配置
```bash
python src/interfaces/cli/main.py analyze-pins examples/sample_stm32_project --output pin_analysis.json
```

### 2. 生成NXP UART代码
```bash
python src/interfaces/cli/main.py generate-uart --config examples/uart_config_example.json --target MCXN947 --output-dir ./generated
```

### 3. 列出所有可用的MCP服务
```bash
python src/interfaces/cli/main.py list-services
```

### 4. 测试特定的MCP服务
```bash
python src/interfaces/cli/main.py test-service analyze_pin_configuration_mcp --input examples/test_pin_analysis_input.json
```

## 📁 项目结构

```
stm32-to-nxp-migration-tool/
├── src/                           # 源代码
│   ├── mcp_services/             # MCP服务实现
│   │   ├── analysis/            # 分析类MCP服务
│   │   ├── codegen/             # 代码生成类MCP服务
│   │   └── base_mcp.py          # MCP服务基类
│   ├── core/                     # 核心组件
│   │   ├── orchestrator.py      # 任务编排器
│   │   ├── quality_gate.py      # 质量门禁
│   │   └── llm_pipeline.py      # LLM代码生成流水线
│   ├── interfaces/               # 用户接口
│   │   ├── cli/                 # 命令行界面
│   │   ├── web/                 # Web界面
│   │   └── vscode/              # VSCode插件
│   └── data/                     # 数据层
│       ├── schemas/             # JSON Schema定义
│       ├── templates/           # 代码模板
│       └── specs/               # MCU规格数据
├── tests/                        # 测试代码
├── docs/                         # 文档
├── examples/                     # 示例项目和配置
└── scripts/                      # 构建和部署脚本
```

## 🔧 MCP服务

### 分析类MCP服务
- `analyze_pin_configuration_mcp` - 引脚配置分析
- `analyze_clock_tree_mcp` - 时钟树分析
- `analyze_uart_init_mcp` - UART配置分析
- `analyze_i2c_init_mcp` - I2C配置分析
- `analyze_rtos_usage_mcp` - RTOS使用分析
- `find_nxp_equivalent_mcp` - NXP MCU匹配

### 代码生成类MCP服务
- `nxp_system_codegen_mcp` - 系统初始化代码生成
- `nxp_uart_codegen_mcp` - UART代码生成
- `nxp_i2c_codegen_mcp` - I2C代码生成
- `nxp_rtos_codegen_mcp` - RTOS代码生成

## 📖 使用示例

### 配置文件示例

**UART配置文件 (uart_config_example.json):**
```json
{
  "uart_configurations": [
    {
      "instance": "USART1",
      "baudrate": 115200,
      "data_bits": 8,
      "stop_bits": 1,
      "parity": "None",
      "flow_control": "None"
    }
  ],
  "target_mcu": "MCXN947"
}
```

### 生成的代码示例

工具会生成完整的NXP MCUXpresso SDK代码，包括：
- 初始化函数
- 配置结构体
- 中断处理程序
- 错误处理机制
- Doxygen风格注释

## 🧪 测试

运行所有测试：
```bash
pytest tests/
```

运行特定测试：
```bash
pytest tests/test_pin_analysis_mcp.py -v
```

生成测试覆盖率报告：
```bash
pytest --cov=src tests/
```

## 📚 文档

详细文档请参考：
- [需求文档](01_需求文档.md)
- [设计文档](02_设计文档.md)
- [实现文档](03_实现文档.md)

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果您遇到问题或有疑问，请：
1. 查看 [FAQ](docs/FAQ.md)
2. 搜索现有的 [Issues](https://github.com/your-repo/stm32-to-nxp-migration-tool/issues)
3. 创建新的 Issue

## 🗺️ 路线图

- [x] 基础MCP服务实现
- [x] CLI界面
- [ ] Web界面
- [ ] VSCode插件
- [ ] LLM集成
- [ ] 质量门禁系统
- [ ] 批量处理支持
- [ ] 更多MCU系列支持

## 👥 团队

- 项目负责人：[您的姓名]
- 核心开发者：[团队成员]

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**注意**：这是一个开发中的项目，某些功能可能还不完整。欢迎反馈和建议！
