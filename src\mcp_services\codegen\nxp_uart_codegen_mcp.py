"""
NXP UART代码生成MCP服务
根据STM32 UART配置生成对应的NXP MCUXpresso SDK代码
"""

from jinja2 import Template
from typing import Dict, Any, List
import logging

from ..base_mcp import CodegenMCP

logger = logging.getLogger(__name__)

class NXPUARTCodegenMCP(CodegenMCP):
    """NXP UART代码生成MCP服务"""
    
    def __init__(self):
        super().__init__("nxp_uart_codegen_mcp")
        self.uart_mapping = {
            # STM32 -> NXP UART实例映射
            "USART1": "LPUART1",
            "USART2": "LPUART2", 
            "USART3": "LPUART3",
            "UART4": "LPUART4",
            "UART5": "LPUART5"
        }
        
        # NXP MCU系列的UART类型映射
        self.mcu_uart_types = {
            "MCXN": "LPUART",  # MCXN系列使用LPUART
            "LPC": "USART",    # LPC系列使用USART
            "Kinetis": "UART", # Kinetis系列使用UART
            "i.MX RT": "LPUART" # i.MX RT系列使用LPUART
        }
        
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成NXP UART初始化代码"""
        uart_configs = input_data.get("uart_configurations", [])
        target_mcu = input_data.get("target_mcu", "MCXN947")
        pin_mappings = input_data.get("pin_mappings", {})
        
        if not uart_configs:
            raise ValueError("未提供UART配置信息")
        
        # 确定目标MCU的UART类型
        uart_type = self._determine_uart_type(target_mcu)
        
        generated_files = {}
        header_includes = set()
        uart_instances = []
        
        for uart_config in uart_configs:
            code_result = self._generate_uart_code(uart_config, target_mcu, uart_type, pin_mappings)
            
            # 收集生成的文件
            instance_name = code_result["instance_mapping"]["nxp"]
            generated_files[f"{instance_name.lower()}_config.c"] = code_result["c_code"]
            generated_files[f"{instance_name.lower()}_config.h"] = code_result["h_code"]
            
            # 收集头文件
            header_includes.update(code_result["h_includes"])
            
            # 收集实例信息
            uart_instances.append(code_result["instance_mapping"])
        
        return {
            "generated_files": generated_files,
            "header_includes": list(header_includes),
            "uart_instances": uart_instances,
            "uart_count": len(uart_configs),
            "target_uart_type": uart_type,
            "code_quality_metrics": self._calculate_code_metrics(generated_files)
        }
    
    def _determine_uart_type(self, target_mcu: str) -> str:
        """根据目标MCU确定UART类型"""
        for mcu_family, uart_type in self.mcu_uart_types.items():
            if mcu_family in target_mcu.upper():
                return uart_type
        
        # 默认使用LPUART
        return "LPUART"
    
    def _generate_uart_code(self, stm32_config: Dict, target_mcu: str, 
                           uart_type: str, pin_mappings: Dict) -> Dict[str, Any]:
        """生成单个UART的初始化代码"""
        stm32_instance = stm32_config["instance"]
        nxp_instance = self.uart_mapping.get(stm32_instance, "LPUART1")
        
        # 根据UART类型调整实例名称
        if uart_type == "USART":
            nxp_instance = nxp_instance.replace("LPUART", "USART")
        elif uart_type == "UART":
            nxp_instance = nxp_instance.replace("LPUART", "UART")
        
        # 生成C代码
        c_code = self._generate_c_code(stm32_config, nxp_instance, uart_type, pin_mappings)
        
        # 生成头文件
        h_code = self._generate_h_code(nxp_instance, uart_type)
        
        # 确定需要的头文件
        h_includes = self._get_required_headers(uart_type)
        
        return {
            "c_code": c_code,
            "h_code": h_code,
            "h_includes": h_includes,
            "instance_mapping": {
                "stm32": stm32_instance,
                "nxp": nxp_instance
            },
            "validation": self._validate_generated_code(c_code)
        }
    
    def _generate_c_code(self, stm32_config: Dict, nxp_instance: str, 
                        uart_type: str, pin_mappings: Dict) -> str:
        """生成C代码"""
        
        # 根据UART类型选择模板
        if uart_type == "LPUART":
            template_str = self._get_lpuart_template()
        elif uart_type == "USART":
            template_str = self._get_usart_template()
        else:
            template_str = self._get_uart_template()
        
        template = Template(template_str)
        
        # 准备模板变量
        template_vars = {
            "instance": nxp_instance,
            "instance_lower": nxp_instance.lower(),
            "baudrate": stm32_config.get("baudrate", 115200),
            "data_bits_enum": self._map_data_bits(stm32_config.get("data_bits", 8), uart_type),
            "stop_bits_enum": self._map_stop_bits(stm32_config.get("stop_bits", 1), uart_type),
            "parity_enum": self._map_parity(stm32_config.get("parity", "None"), uart_type),
            "flow_control": stm32_config.get("flow_control", "None"),
            "clock_source": self._get_clock_source(uart_type),
            "error_handling": True,
            "doxygen_comments": True
        }
        
        return template.render(**template_vars).strip()
    
    def _get_lpuart_template(self) -> str:
        """获取LPUART代码模板"""
        return '''
/**
 * @file    {{instance_lower}}_config.c
 * @brief   {{instance}} configuration and initialization
 * <AUTHOR> Migration Tool
 * @date    Generated by STM32-to-NXP Migration Tool
 */

#include "{{instance_lower}}_config.h"
#include "fsl_lpuart.h"
#include "fsl_clock.h"
#include "pin_mux.h"

/*******************************************************************************
 * Definitions
 ******************************************************************************/
#define {{instance}}_BAUDRATE {{baudrate}}U
#define {{instance}}_CLK_FREQ {{clock_source}}

/*******************************************************************************
 * Variables
 ******************************************************************************/
static lpuart_handle_t {{instance_lower}}_handle;
static uint8_t {{instance_lower}}_background_buffer[64];

/*******************************************************************************
 * Code
 ******************************************************************************/

/**
 * @brief Initialize {{instance}} peripheral
 * @return Status of initialization (kStatus_Success or error code)
 */
status_t {{instance}}_Init(void)
{
    lpuart_config_t {{instance_lower}}_config;
    status_t status;
    
    /* Get default configuration */
    LPUART_GetDefaultConfig(&{{instance_lower}}_config);
    
    /* Configure UART parameters */
    {{instance_lower}}_config.baudRate_Bps = {{instance}}_BAUDRATE;
    {{instance_lower}}_config.dataBitsCount = {{data_bits_enum}};
    {{instance_lower}}_config.stopBitCount = {{stop_bits_enum}};
    {{instance_lower}}_config.parityMode = {{parity_enum}};
    {{instance_lower}}_config.enableTx = true;
    {{instance_lower}}_config.enableRx = true;
    
    /* Initialize LPUART */
    status = LPUART_Init({{instance}}, &{{instance_lower}}_config, {{instance}}_CLK_FREQ);
    if (status != kStatus_Success)
    {
        return status;
    }
    
    /* Create handle for non-blocking operations */
    LPUART_TransferCreateHandle({{instance}}, &{{instance_lower}}_handle, 
                               {{instance}}_Callback, NULL);
    
    /* Enable RX interrupt */
    LPUART_EnableInterrupts({{instance}}, kLPUART_RxDataRegFullInterruptEnable);
    
    return kStatus_Success;
}

/**
 * @brief Deinitialize {{instance}} peripheral
 */
void {{instance}}_Deinit(void)
{
    LPUART_Deinit({{instance}});
}

/**
 * @brief Send data via {{instance}}
 * @param data Pointer to data buffer
 * @param length Number of bytes to send
 * @return Status of operation
 */
status_t {{instance}}_SendData(const uint8_t *data, size_t length)
{
    lpuart_transfer_t transfer;
    
    if (data == NULL || length == 0)
    {
        return kStatus_InvalidArgument;
    }
    
    transfer.data = (uint8_t *)data;
    transfer.dataSize = length;
    
    return LPUART_TransferSendNonBlocking({{instance}}, &{{instance_lower}}_handle, &transfer);
}

/**
 * @brief Receive data via {{instance}}
 * @param data Pointer to receive buffer
 * @param length Number of bytes to receive
 * @return Status of operation
 */
status_t {{instance}}_ReceiveData(uint8_t *data, size_t length)
{
    lpuart_transfer_t transfer;
    
    if (data == NULL || length == 0)
    {
        return kStatus_InvalidArgument;
    }
    
    transfer.data = data;
    transfer.dataSize = length;
    
    return LPUART_TransferReceiveNonBlocking({{instance}}, &{{instance_lower}}_handle, &transfer);
}

/**
 * @brief {{instance}} transfer callback function
 * @param base LPUART peripheral base address
 * @param handle LPUART handle
 * @param status Transfer status
 * @param userData User data
 */
void {{instance}}_Callback(LPUART_Type *base, lpuart_handle_t *handle, 
                          status_t status, void *userData)
{
    /* User can implement custom callback logic here */
    if (status == kStatus_LPUART_TxIdle)
    {
        /* Transmission completed */
    }
    else if (status == kStatus_LPUART_RxIdle)
    {
        /* Reception completed */
    }
    else if (status == kStatus_LPUART_RxRingBufferOverrun)
    {
        /* Ring buffer overrun */
    }
}

/**
 * @brief {{instance}} IRQ handler
 */
void {{instance}}_IRQHandler(void)
{
    LPUART_TransferHandleIRQ({{instance}}, &{{instance_lower}}_handle);
}
        '''
    
    def _generate_h_code(self, nxp_instance: str, uart_type: str) -> str:
        """生成头文件代码"""
        template_str = '''
/**
 * @file    {{instance_lower}}_config.h
 * @brief   {{instance}} configuration header
 * <AUTHOR> Migration Tool
 */

#ifndef {{instance_upper}}_CONFIG_H
#define {{instance_upper}}_CONFIG_H

#include "fsl_common.h"
{% if uart_type == "LPUART" %}
#include "fsl_lpuart.h"
{% elif uart_type == "USART" %}
#include "fsl_usart.h"
{% else %}
#include "fsl_uart.h"
{% endif %}

/*******************************************************************************
 * API
 ******************************************************************************/

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize {{instance}} peripheral
 * @return Status of initialization
 */
status_t {{instance}}_Init(void);

/**
 * @brief Deinitialize {{instance}} peripheral
 */
void {{instance}}_Deinit(void);

/**
 * @brief Send data via {{instance}}
 * @param data Pointer to data buffer
 * @param length Number of bytes to send
 * @return Status of operation
 */
status_t {{instance}}_SendData(const uint8_t *data, size_t length);

/**
 * @brief Receive data via {{instance}}
 * @param data Pointer to receive buffer
 * @param length Number of bytes to receive
 * @return Status of operation
 */
status_t {{instance}}_ReceiveData(uint8_t *data, size_t length);

/**
 * @brief {{instance}} transfer callback function
 */
{% if uart_type == "LPUART" %}
void {{instance}}_Callback(LPUART_Type *base, lpuart_handle_t *handle, 
                          status_t status, void *userData);
{% endif %}

/**
 * @brief {{instance}} IRQ handler
 */
void {{instance}}_IRQHandler(void);

#ifdef __cplusplus
}
#endif

#endif /* {{instance_upper}}_CONFIG_H */
        '''
        
        template = Template(template_str)
        return template.render(
            instance=nxp_instance,
            instance_lower=nxp_instance.lower(),
            instance_upper=nxp_instance.upper(),
            uart_type=uart_type
        ).strip()
    
    def _map_data_bits(self, stm32_data_bits: int, uart_type: str) -> str:
        """映射数据位设置"""
        if uart_type == "LPUART":
            mapping = {
                8: "kLPUART_EightDataBits",
                9: "kLPUART_NineDataBits"
            }
        elif uart_type == "USART":
            mapping = {
                7: "kUSART_7BitsPerChar",
                8: "kUSART_8BitsPerChar",
                9: "kUSART_9BitsPerChar"
            }
        else:  # UART
            mapping = {
                8: "kUART_EightDataBits",
                9: "kUART_NineDataBits"
            }
        
        return mapping.get(stm32_data_bits, mapping[8])
    
    def _map_stop_bits(self, stm32_stop_bits: int, uart_type: str) -> str:
        """映射停止位设置"""
        if uart_type == "LPUART":
            mapping = {
                1: "kLPUART_OneStopBit",
                2: "kLPUART_TwoStopBit"
            }
        elif uart_type == "USART":
            mapping = {
                1: "kUSART_OneStopBit",
                2: "kUSART_TwoStopBit"
            }
        else:  # UART
            mapping = {
                1: "kUART_OneStopBit",
                2: "kUART_TwoStopBit"
            }
        
        return mapping.get(stm32_stop_bits, mapping[1])
    
    def _map_parity(self, stm32_parity: str, uart_type: str) -> str:
        """映射奇偶校验设置"""
        if uart_type == "LPUART":
            mapping = {
                "None": "kLPUART_ParityDisabled",
                "Even": "kLPUART_ParityEven",
                "Odd": "kLPUART_ParityOdd"
            }
        elif uart_type == "USART":
            mapping = {
                "None": "kUSART_ParityDisabled",
                "Even": "kUSART_ParityEven",
                "Odd": "kUSART_ParityOdd"
            }
        else:  # UART
            mapping = {
                "None": "kUART_ParityDisabled",
                "Even": "kUART_ParityEven",
                "Odd": "kUART_ParityOdd"
            }
        
        return mapping.get(stm32_parity, mapping["None"])
    
    def _get_clock_source(self, uart_type: str) -> str:
        """获取时钟源"""
        if uart_type == "LPUART":
            return "CLOCK_GetFreq(kCLOCK_ScgSysPllAsyncDiv2Clk)"
        elif uart_type == "USART":
            return "CLOCK_GetFreq(kCLOCK_FlexCommClk)"
        else:
            return "CLOCK_GetFreq(kCLOCK_BusClk)"
    
    def _get_required_headers(self, uart_type: str) -> List[str]:
        """获取需要的头文件"""
        common_headers = ["fsl_common.h", "fsl_clock.h"]
        
        if uart_type == "LPUART":
            return common_headers + ["fsl_lpuart.h"]
        elif uart_type == "USART":
            return common_headers + ["fsl_usart.h"]
        else:
            return common_headers + ["fsl_uart.h"]
    
    def _calculate_code_metrics(self, generated_files: Dict[str, str]) -> Dict[str, Any]:
        """计算代码质量指标"""
        metrics = {
            "total_files": len(generated_files),
            "total_lines": 0,
            "comment_lines": 0,
            "function_count": 0,
            "has_error_handling": False
        }
        
        for filename, code in generated_files.items():
            if isinstance(code, str):
                lines = code.split('\n')
                metrics["total_lines"] += len(lines)
                
                # 统计注释行
                comment_lines = sum(1 for line in lines 
                                  if line.strip().startswith('*') or 
                                     line.strip().startswith('//') or
                                     line.strip().startswith('/*'))
                metrics["comment_lines"] += comment_lines
                
                # 统计函数数量
                function_count = code.count('status_t ') + code.count('void ')
                metrics["function_count"] += function_count
                
                # 检查错误处理
                if "kStatus_Success" in code or "return status" in code:
                    metrics["has_error_handling"] = True
        
        # 计算注释率
        if metrics["total_lines"] > 0:
            metrics["comment_ratio"] = metrics["comment_lines"] / metrics["total_lines"]
        else:
            metrics["comment_ratio"] = 0.0
        
        return metrics
    
    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "uart_configurations": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "instance": {"type": "string"},
                            "baudrate": {"type": "integer"},
                            "data_bits": {"type": "integer"},
                            "stop_bits": {"type": "integer"},
                            "parity": {"type": "string"},
                            "flow_control": {"type": "string"}
                        },
                        "required": ["instance", "baudrate"]
                    }
                },
                "target_mcu": {"type": "string"},
                "pin_mappings": {"type": "object"}
            },
            "required": ["uart_configurations"]
        }
    
    def get_output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "generated_files": {
                    "type": "object",
                    "patternProperties": {
                        ".*\\.(c|h)$": {"type": "string"}
                    }
                },
                "header_includes": {
                    "type": "array",
                    "items": {"type": "string"}
                },
                "uart_instances": {
                    "type": "array",
                    "items": {"type": "object"}
                },
                "uart_count": {"type": "integer"},
                "target_uart_type": {"type": "string"},
                "code_quality_metrics": {"type": "object"}
            }
        }
