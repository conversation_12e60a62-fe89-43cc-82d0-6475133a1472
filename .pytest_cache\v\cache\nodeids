["tests/test_full_migration.py::test_full_migration_of_complex_project", "tests/test_mcp_integration.py::TestMCPIntegration::test_analyze_pins_tool", "tests/test_mcp_integration.py::TestMCPIntegration::test_error_response_format", "tests/test_mcp_integration.py::TestMCPIntegration::test_generate_uart_tool", "tests/test_mcp_integration.py::TestMCPIntegration::test_initialize_request", "tests/test_mcp_integration.py::TestMCPIntegration::test_invalid_json_request", "tests/test_mcp_integration.py::TestMCPIntegration::test_list_services_tool", "tests/test_mcp_integration.py::TestMCPIntegration::test_list_tools_request", "tests/test_mcp_integration.py::TestMCPIntegration::test_tool_schema_validation", "tests/test_mcp_integration.py::TestMCPIntegration::test_unknown_method", "tests/test_mcp_integration.py::TestMCPIntegration::test_unknown_tool", "tests/test_mcp_integration.py::TestMCPServerStandalone::test_format_pin_analysis_result", "tests/test_mcp_integration.py::TestMCPServerStandalone::test_format_uart_generation_result", "tests/test_mcp_integration.py::TestMCPServerStandalone::test_server_initialization"]