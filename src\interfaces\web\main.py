
# src/interfaces/web/main.py
from fastapi import FastAPI, File, UploadFile
from pathlib import Path
import shutil

from src.core.orchestrator import MigrationOrchestrator

app = FastAPI()

@app.post("/migrate/")
async def migrate_project(file: UploadFile = File(...)):
    temp_dir = Path("/tmp/migration_tool")
    temp_dir.mkdir(exist_ok=True)
    project_path = temp_dir / file.filename

    with project_path.open("wb") as buffer:
        shutil.copyfileobj(file.file, buffer)

    orchestrator = MigrationOrchestrator("config/default.json")
    result = await orchestrator.execute_migration(str(project_path), "MCXN947")

    return result
