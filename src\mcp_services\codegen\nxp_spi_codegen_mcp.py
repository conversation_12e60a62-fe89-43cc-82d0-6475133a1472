
# src/mcp_services/codegen/nxp_spi_codegen_mcp.py
from jinja2 import Template
from typing import Dict, Any

from ..base_mcp import BaseMCP

class NXPSPICodegenMCP(BaseMCP):
    """NXP SPI代码生成MCP服务"""

    def __init__(self):
        super().__init__("nxp_spi_codegen_mcp")
        self.spi_mapping = {
            "SPI1": "LPSPI1",
            "SPI2": "LPSPI2",
        }

    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        stm32_spi_configs = input_data["spi_configurations"]
        
        generated_files = {}
        for spi_config in stm32_spi_configs:
            code_result = self._generate_spi_code(spi_config)
            instance_name = self.spi_mapping.get(spi_config["instance"], "LPSPI1")
            generated_files[f"{instance_name.lower()}_config.c"] = code_result["c_code"]
        
        return {
            "generated_files": generated_files,
            "header_includes": ["fsl_lpspi.h", "fsl_clock.h"]
        }

    def _generate_spi_code(self, stm32_config: Dict) -> Dict[str, Any]:
        nxp_instance = self.spi_mapping.get(stm32_config["instance"], "LPSPI1")
        
        template = Template("""
/* {{nxp_instance}} initialization */
void {{nxp_instance}}_Init(void)
{
    lpspi_master_config_t {{nxp_instance.lower()}}_masterConfig;

    LPSPI_MasterGetDefaultConfig(&{{nxp_instance.lower()}}_masterConfig);
    {{nxp_instance.lower()}}_masterConfig.baudRate = 1000000; // Example baudrate
    {{nxp_instance.lower()}}_masterConfig.bitsPerFrame = {{data_size}};
    {{nxp_instance.lower()}}_masterConfig.cpol = {{cpol}};
    {{nxp_instance.lower()}}_masterConfig.cpha = {{cpha}};

    LPSPI_MasterInit({{nxp_instance}}, &{{nxp_instance.lower()}}_masterConfig, CLOCK_GetFreq(kCLOCK_ScgSysPllAsyncDiv2Clk));
}
        """)
        
        code = template.render(
            nxp_instance=nxp_instance,
            data_size=stm32_config["data_size"],
            cpol=self._map_cpol(stm32_config["cpol"]),
            cpha=self._map_cpha(stm32_config["cpha"])
        )
        
        return {
            "c_code": code.strip()
        }

    def _map_cpol(self, cpol: str) -> str:
        return "kLPSPI_ClockPolarityActiveHigh" if cpol == "High" else "kLPSPI_ClockPolarityActiveLow"

    def _map_cpha(self, cpha: str) -> str:
        return "kLPSPI_ClockPhaseSecondEdge" if cpha == "2Edge" else "kLPSPI_ClockPhaseFirstEdge"

    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "spi_configurations": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "instance": {"type": "string"},
                            "data_size": {"type": "integer"},
                            "cpol": {"type": "string"},
                            "cpha": {"type": "string"}
                        }
                    }
                }
            }
        }

    def get_output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "generated_files": {
                    "type": "object"
                },
                "header_includes": {
                    "type": "array"
                }
            }
        }
