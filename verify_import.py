import sys
import os
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("--- Verification Script Start ---")
print(f"Current Working Directory: {os.getcwd()}")
print("sys.path:")
for p in sys.path:
    print(f"  - {p}")
print("-----------------------------")

try:
    from src.core.orchestrator import MigrationOrchestrator
    print("SUCCESS: Successfully imported MigrationOrchestrator.")
except ImportError as e:
    print(f"FAILURE: Failed to import MigrationOrchestrator.")
    print(f"Error: {e}")
except Exception as e:
    print(f"An unexpected error occurred: {e}")

print("--- Verification Script End ---")
