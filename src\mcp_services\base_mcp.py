"""
MCP服务基类，定义统一的接口规范
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import json
import time
from datetime import datetime, timezone
import jsonschema
from jsonschema import ValidationError
import logging

logger = logging.getLogger(__name__)

class BaseMCP(ABC):
    """MCP服务基类，定义统一的接口规范"""
    
    def __init__(self, name: str, version: str = "1.0.0"):
        self.name = name
        self.version = version
        self.schema_validator = jsonschema.Draft7Validator
        
    @abstractmethod
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理输入数据，返回标准格式的结果"""
        pass
    
    @abstractmethod
    def get_input_schema(self) -> Dict[str, Any]:
        """返回输入数据的JSON Schema"""
        pass
    
    @abstractmethod
    def get_output_schema(self) -> Dict[str, Any]:
        """返回输出数据的JSON Schema"""
        pass
    
    def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行MCP服务，包含完整的错误处理和验证"""
        start_time = time.time()
        
        try:
            # 输入验证
            self._validate_input(input_data)
            
            # 执行处理逻辑
            result_data = self.process(input_data)
            
            # 输出验证
            self._validate_output(result_data)
            
            # 构建标准响应
            response = self._build_response(
                status="success",
                data=result_data,
                execution_time=time.time() - start_time
            )
            
            logger.info(f"MCP服务 {self.name} 执行成功", extra={
                'mcp_name': self.name,
                'execution_time': response['execution_time_ms']
            })
            
        except ValidationError as e:
            response = self._build_response(
                status="error",
                error={
                    "code": "E_VALIDATION_FAILED",
                    "message": str(e),
                    "suggestion": "检查输入数据格式"
                },
                execution_time=time.time() - start_time
            )
            logger.error(f"MCP服务 {self.name} 验证失败: {str(e)}")
            
        except Exception as e:
            response = self._build_response(
                status="error",
                error={
                    "code": "E_PROCESSING_FAILED",
                    "message": str(e),
                    "suggestion": "检查服务配置和输入数据"
                },
                execution_time=time.time() - start_time
            )
            logger.error(f"MCP服务 {self.name} 处理失败: {str(e)}")
            
        return response
    
    def _validate_input(self, data: Dict[str, Any]):
        """验证输入数据"""
        schema = self.get_input_schema()
        jsonschema.validate(data, schema)
    
    def _validate_output(self, data: Dict[str, Any]):
        """验证输出数据"""
        schema = self.get_output_schema()
        jsonschema.validate(data, schema)
    
    def _build_response(self, status: str, data: Optional[Dict] = None, 
                       error: Optional[Dict] = None, execution_time: float = 0):
        """构建标准响应格式"""
        confidence_score = self._calculate_confidence_score(data, error)
        data_quality = self._assess_data_quality(data)
        
        return {
            "mcp_name": self.name,
            "status": status,
            "version": self.version,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "execution_time_ms": int(execution_time * 1000),
            "data": data or {},
            "metadata": {
                "confidence_score": confidence_score,
                "data_quality": data_quality,
                "validation_passed": status == "success"
            },
            "error": error
        }
    
    def _calculate_confidence_score(self, data: Optional[Dict], error: Optional[Dict]) -> float:
        """计算置信度分数，子类可以重写此方法"""
        if error:
            return 0.0
        if not data:
            return 0.5
        return 0.95  # 默认高置信度
    
    def _assess_data_quality(self, data: Optional[Dict]) -> str:
        """评估数据质量，子类可以重写此方法"""
        if not data:
            return "low"
        
        # 简单的数据质量评估
        if len(data) > 5:
            return "high"
        elif len(data) > 2:
            return "medium"
        else:
            return "low"
    
    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.__doc__ or "MCP服务",
            "input_schema": self.get_input_schema(),
            "output_schema": self.get_output_schema()
        }


class AnalysisMCP(BaseMCP):
    """分析类MCP服务基类"""
    
    def __init__(self, name: str, version: str = "1.0.0"):
        super().__init__(name, version)
    
    def _calculate_confidence_score(self, data: Optional[Dict], error: Optional[Dict]) -> float:
        """分析类MCP的置信度计算"""
        if error:
            return 0.0
        if not data:
            return 0.3
        
        # 基于分析结果的数量和完整性计算置信度
        total_items = 0
        valid_items = 0
        
        for key, value in data.items():
            if isinstance(value, list):
                total_items += len(value)
                valid_items += len([item for item in value if item])
            elif isinstance(value, dict):
                total_items += len(value)
                valid_items += len([v for v in value.values() if v])
            elif value:
                total_items += 1
                valid_items += 1
        
        if total_items == 0:
            return 0.5
        
        return min(valid_items / total_items, 1.0)


class CodegenMCP(BaseMCP):
    """代码生成类MCP服务基类"""
    
    def __init__(self, name: str, version: str = "1.0.0"):
        super().__init__(name, version)
    
    def _calculate_confidence_score(self, data: Optional[Dict], error: Optional[Dict]) -> float:
        """代码生成类MCP的置信度计算"""
        if error:
            return 0.0
        if not data:
            return 0.2
        
        # 基于生成代码的质量指标计算置信度
        confidence = 0.7  # 基础置信度
        
        if "generated_files" in data:
            files = data["generated_files"]
            if isinstance(files, dict) and len(files) > 0:
                confidence += 0.2
                
                # 检查代码质量指标
                for filename, code in files.items():
                    if isinstance(code, str) and len(code) > 100:
                        confidence += 0.05
                    if "/*" in code or "//" in code:  # 包含注释
                        confidence += 0.02
        
        return min(confidence, 1.0)
    
    def _validate_generated_code(self, code: str) -> Dict[str, Any]:
        """验证生成的代码质量"""
        validation_result = {
            "syntax_valid": True,
            "has_comments": False,
            "has_error_handling": False,
            "line_count": 0,
            "issues": []
        }
        
        if not code or not isinstance(code, str):
            validation_result["syntax_valid"] = False
            validation_result["issues"].append("代码为空或格式错误")
            return validation_result
        
        lines = code.split('\n')
        validation_result["line_count"] = len(lines)
        
        # 检查注释
        if "/*" in code or "//" in code:
            validation_result["has_comments"] = True
        
        # 检查错误处理
        error_handling_keywords = ["if", "return", "error", "ERROR", "fail", "FAIL"]
        if any(keyword in code for keyword in error_handling_keywords):
            validation_result["has_error_handling"] = True
        
        # 基本语法检查
        if code.count('{') != code.count('}'):
            validation_result["syntax_valid"] = False
            validation_result["issues"].append("大括号不匹配")
        
        if code.count('(') != code.count(')'):
            validation_result["syntax_valid"] = False
            validation_result["issues"].append("小括号不匹配")
        
        return validation_result


class MCPServiceRegistry:
    """MCP服务注册表"""
    
    def __init__(self):
        self._services = {}
    
    def register(self, service: BaseMCP):
        """注册MCP服务"""
        self._services[service.name] = service
        logger.info(f"注册MCP服务: {service.name}")
    
    def get_service(self, name: str) -> Optional[BaseMCP]:
        """获取MCP服务"""
        return self._services.get(name)
    
    def list_services(self) -> Dict[str, Dict[str, Any]]:
        """列出所有注册的服务"""
        return {name: service.get_service_info() 
                for name, service in self._services.items()}
    
    def unregister(self, name: str) -> bool:
        """注销MCP服务"""
        if name in self._services:
            del self._services[name]
            logger.info(f"注销MCP服务: {name}")
            return True
        return False


# 全局服务注册表实例
service_registry = MCPServiceRegistry()
