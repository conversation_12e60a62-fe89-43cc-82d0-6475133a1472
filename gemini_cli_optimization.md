# Gemini CLI 高级配置与性能优化指南

Gemini CLI 是一款功能强大的工具，通过高级配置和性能优化，可以显著提升其效率和用户体验。本文档汇总了全网关于 Gemini CLI 的高级配置、性能调优技巧和最佳实践，旨在帮助用户充分发挥其最大性能。

## 一、核心配置文件：`settings.json`

`settings.json` 是 Gemini CLI 的核心配置文件，用于管理和自定义其行为。

### 1.1. 配置文件位置和优先级

Gemini CLI 按照以下顺序加载配置，后面的配置会覆盖前面的配置：

1.  **默认值**: CLI 内置的默认设置。
2.  **用户级配置文件**: `~/.gemini/settings.json`，适用于所有项目的全局设置。
3.  **项目级配置文件**: `.gemini/settings.json`，位于项目根目录，用于特定项目的设置。
4.  **环境变量**: 系统或会话级别的变量，通常从 `.env` 文件加载。
5.  **命令行参数**: 运行时直接传递的参数，具有最高优先级。

### 1.2. 关键配置项

以下是 `settings.json` 中一些关键的配置项：

-   `"theme"`: 自定义 CLI 的颜色主题。
-   `"selectedAuthType"`: 指定身份验证方法，如 `"oauth-personal"`。
-   `"model"`: 设置默认使用的 Gemini 模型。
-   `"sandbox"`: 设置为 `true` 时，启用沙盒环境执行命令，增强安全性。
-   `"checkpointing"`: 启用后，在修改文件前自动保存快照，允许恢复到之前的状态。
-   `"telemetry"`: 配置将性能指标和日志发送到指定目标，用于监控和调试。
-   `"mcpServers"`: 配置模型上下文协议 (MCP) 服务器，扩展 Gemini 的功能。
-   `"autoAccept"`: 自动批准安全的、只读的工具调用。
-   `"usageStatisticsEnabled"`: 设置为 `false` 以禁用发送使用情况统计信息。
-   `"contextFileName"`: 指定自定义上下文文件。

## 二、性能调优

### 2.1. 缓存机制

Gemini CLI 默认启用**令牌缓存**，以提高性能并降低成本。

-   **自动缓存**: 在使用 API 密钥进行身份验证时默认启用，可重用先前交互中的令牌。
-   **查看缓存统计**: 使用 `/stats` 命令查看缓存节省的统计信息。
-   **清除认证缓存**: 删除 `~/.gemini/auth` 目录以清除认证缓存。

### 2.2. 超时和重试

-   **超时设置**: 在 `settings.json` 的 `mcpServers` 块中为 MCP 服务器配置 `timeout`（以毫秒为单位）。
-   **重试逻辑**: Gemini CLI 内置了重试逻辑，以应对网络波动和 API 限制。

### 2.3. 高效执行

-   **使用 `pnpm dlx`**: 推荐使用 `pnpm dlx @google/gemini-cli` 来运行 CLI，以获得性能、安全性和可靠性的平衡。
-   **设置 Shell 别名**: 为 `pnpm dlx @google/gemini-cli` 创建一个简短的别名（如 `g`），以提高日常工作效率。

## 三、最佳实践

### 3.1. 工作流和使用技巧

-   **项目上下文 (`GEMINI.md`)**: 在项目根目录创建 `GEMINI.md` 文件，作为 AI 的持久记忆，定义项目架构、编码标准等。
-   **分解复杂任务**: 将大型、模糊的请求分解为更小、更具体的增量提示。
-   **自定义斜杠命令**: 在 `.toml` 文件中定义可重用的提示作为自定义斜杠命令，以简化重复性工作流程。
-   **测试驱动开发 (TDD)**: 使用 Gemini 生成测试，先编写失败的测试，然后实现功能以使测试通过。
-   **检查点 (`checkpointing`)**: 使用 `gemini -c` 创建快照，以便在需要时回滚更改。

### 3.2. 善用工具

-   **Shell 集成**: 在交互模式下，使用 `!` 执行 shell 命令（如 `!npm test`），使用 `read-file` 将文件加载到内存中。
-   **文件引用**: 使用 `@` 命令引用特定文件，以帮助完成诸如遵循 UI/UX 指南或数据库模式之类的任务。
-   **发现工具**: 使用 `/tools` 命令查看 Gemini CLI 中所有可用工具的列表。
-   **多模态功能**: 对于与 UI 相关的任务，可以向 Gemini 提供图像以获取有关调试或改进界面的帮助。

## 四、模型上下文协议 (MCP) 服务器优化

MCP 服务器是扩展 Gemini CLI 功能和性能的关键。

### 4.1. 服务器配置

-   **验证配置**: 仔细检查 `settings.json` 中 MCP 服务器的 `command`、`args` 和 `cwd` 是否正确。
-   **使用高效运行时**: 如果 MCP 服务器是使用 Python 编写的，请考虑使用更快的环境和包管理器，如 `uv`。
-   **异步操作**: 为了获得更好的性能，请确保 MCP 服务器是异步且非阻塞的。

### 4.2. 缓存和数据管理

-   **实现缓存**: 如果 MCP 服务器频繁请求相同的数据，请实现缓存机制以存储和检索本地响应。
-   **数据过滤**: 在查询大型数据集时，仅过滤和请求必要的信息，以最大程度地减少数据传输和处理开销。

### 4.3. 特定工具的优化

-   **文件系统 MCP**: 在大型代码库上使用文件系统 MCP 服务器进行操作时，请使用支持模式匹配的工具进行精确编辑。
-   **数据库 MCP**: 对于数据库 MCP，请在仅需要分析数据库结构时使用只读模式。
-   **Web 抓取 MCP**: 如果使用 MCP 服务器进行 Web 抓取，请注意速率限制并在设置中进行适当配置。

### 4.4. 网络和 API 管理

-   **API 密钥配置**: 确保根据特定 MCP 服务器的要求，将 API 密钥正确设置为环境变量或在配置文件中。
-   **安全稳定的连接**: 运行 MCP 服务器的计算机应具有稳定的网络连接。

通过应用这些高级配置和优化技巧，您可以显著提升 Gemini CLI 的性能和效率，使其成为您开发工作流程中更强大的助手。
