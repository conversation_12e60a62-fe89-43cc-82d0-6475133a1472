{"dependency_graph": {"analyze_uart_init_mcp": ["analyze_clock_tree_mcp"], "analyze_i2c_init_mcp": ["analyze_clock_tree_mcp"], "analyze_spi_init_mcp": ["analyze_clock_tree_mcp"], "analyze_adc_init_mcp": ["analyze_clock_tree_mcp"], "analyze_timer_init_mcp": ["analyze_clock_tree_mcp"], "nxp_uart_codegen_mcp": ["analyze_uart_init_mcp"], "nxp_i2c_codegen_mcp": ["analyze_i2c_init_mcp"], "nxp_spi_codegen_mcp": ["analyze_spi_init_mcp"], "nxp_adc_codegen_mcp": ["analyze_adc_init_mcp"], "nxp_timer_codegen_mcp": ["analyze_timer_init_mcp"]}, "quality_gates": {"min_line_coverage": 85, "min_branch_coverage": 75, "max_cyclomatic_complexity": 10, "static_analysis_errors": 0, "compilation_warnings": 0}}