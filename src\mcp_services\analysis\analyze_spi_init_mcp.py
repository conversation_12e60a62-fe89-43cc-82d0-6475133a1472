
# src/mcp_services/analysis/analyze_spi_init_mcp.py
import configparser
from pathlib import Path
from typing import Dict, Any, List

from ..base_mcp import BaseMCP

class AnalyzeSPIInitMCP(BaseMCP):
    """SPI配置分析MCP服务"""

    def __init__(self):
        super().__init__("analyze_spi_init_mcp")

    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        project_path = Path(input_data["project_path"])
        ioc_file = self._find_ioc_file(project_path)
        
        spi_configs = self._parse_ioc_for_spi(ioc_file)
        
        return {
            "spi_configurations": spi_configs
        }

    def _find_ioc_file(self, project_path: Path) -> Path:
        ioc_files = list(project_path.glob("**/*.ioc"))
        if not ioc_files:
            raise ValueError("未找到STM32CubeMX配置文件(.ioc)")
        return ioc_files[0]

    def _parse_ioc_for_spi(self, ioc_file: Path) -> List[Dict[str, Any]]:
        config = configparser.ConfigParser()
        config.read(ioc_file, encoding='utf-8')
        
        spi_instances = [s for s in config.sections() if s.startswith('SPI')]
        configs = []

        for instance in spi_instances:
            configs.append({
                "instance": instance,
                "mode": config.get(instance, 'Mode', fallback='Master'),
                "data_size": int(config.get(instance, 'DataSize', fallback='8')),
                "cpol": config.get(instance, 'CPOL', fallback='Low'),
                "cpha": config.get(instance, 'CPHA', fallback='1Edge'),
            })
        return configs

    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "project_path": {"type": "string"}
            },
            "required": ["project_path"]
        }

    def get_output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "spi_configurations": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "instance": {"type": "string"},
                            "mode": {"type": "string"},
                            "data_size": {"type": "integer"},
                            "cpol": {"type": "string"},
                            "cpha": {"type": "string"}
                        }
                    }
                }
            }
        }
