{"uart_configurations": [{"instance": "USART1", "baudrate": 115200, "data_bits": 8, "stop_bits": 1, "parity": "None", "flow_control": "None", "mode": "TX_RX", "pins": {"tx_pin": "PA9", "rx_pin": "PA10"}}, {"instance": "USART2", "baudrate": 9600, "data_bits": 8, "stop_bits": 1, "parity": "Even", "flow_control": "RTS_CTS", "mode": "TX_RX", "pins": {"tx_pin": "PA2", "rx_pin": "PA3", "rts_pin": "PA1", "cts_pin": "PA0"}}], "pin_mappings": {"PA9": "PTA2", "PA10": "PTA1", "PA2": "PTB17", "PA3": "PTB16", "PA1": "PTB18", "PA0": "PTB19"}, "target_mcu": "MCXN947", "generation_options": {"include_dma": false, "include_interrupts": true, "error_handling": true, "doxygen_comments": true}}