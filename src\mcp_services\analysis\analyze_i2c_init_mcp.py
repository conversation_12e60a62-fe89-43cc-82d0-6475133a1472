
# src/mcp_services/analysis/analyze_i2c_init_mcp.py
import configparser
import re
from pathlib import Path
from typing import Dict, Any, List

from ..base_mcp import BaseMCP

class AnalyzeI2CInitMCP(BaseMCP):
    """I2C配置分析MCP服务"""

    def __init__(self):
        super().__init__("analyze_i2c_init_mcp")

    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        project_path = Path(input_data["project_path"])
        ioc_file = self._find_ioc_file(project_path)
        
        i2c_configs = self._parse_ioc_for_i2c(ioc_file)
        
        return {
            "i2c_configurations": i2c_configs
        }

    def _find_ioc_file(self, project_path: Path) -> Path:
        ioc_files = list(project_path.glob("**/*.ioc"))
        if not ioc_files:
            raise ValueError("未找到STM32CubeMX配置文件(.ioc)")
        return ioc_files[0]

    def _parse_ioc_for_i2c(self, ioc_file: Path) -> List[Dict[str, Any]]:
        config = configparser.ConfigParser()
        config.read(ioc_file, encoding='utf-8')
        
        i2c_instances = [s for s in config.sections() if s.startswith('I2C')]
        configs = []

        for instance in i2c_instances:
            configs.append({
                "instance": instance,
                "timing_hz": int(config.get(instance, 'I2C_Timing', fallback='100000')),
                "addressing_mode": 7 if '7' in config.get(instance, 'AddressingMode', fallback='7BIT') else 10
            })
        return configs

    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "project_path": {"type": "string"}
            },
            "required": ["project_path"]
        }

    def get_output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "i2c_configurations": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "instance": {"type": "string"},
                            "timing_hz": {"type": "integer"},
                            "addressing_mode": {"type": "integer"}
                        }
                    }
                }
            }
        }
