
# tests/test_full_migration.py
import pytest
from pathlib import Path
import sys

sys.path.append(str(Path(__file__).parent.parent))

from src.core.orchestrator import MigrationOrchestrator

@pytest.mark.asyncio
async def test_full_migration_of_complex_project():
    # Setup
    project_path = "tests/fixtures/complex_project.ioc"
    target_mcu = "MIMXRT1052DVL6B"

    # Create a dummy project structure
    project_dir = Path(project_path).parent
    project_dir.mkdir(exist_ok=True)
    (project_dir / "main.c").touch()

    orchestrator = MigrationOrchestrator("config/default.json")

    # Execute
    result = await orchestrator.execute_migration(str(project_dir), target_mcu)

    # Assert
    assert result["status"] == "success"
    assert "generated_files" in result
    assert "quality_report" in result

    # Check for generated files
    assert "lpi2c1_config.c" in result["generated_files"]
    assert "lpspi1_config.c" in result["generated_files"]
    assert "adc1_config.c" in result["generated_files"]
    assert "tpm1_config.c" in result["generated_files"]

    # Check quality report
    assert result["quality_report"]["compilation_warnings"] == 0
    assert result["quality_report"]["static_analysis_errors"] == 0
