
# src/mcp_services/analysis/analyze_adc_init_mcp.py
import configparser
from pathlib import Path
from typing import Dict, Any, List

from ..base_mcp import BaseMCP

class AnalyzeADCInitMCP(BaseMCP):
    """ADC配置分析MCP服务"""

    def __init__(self):
        super().__init__("analyze_adc_init_mcp")

    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        project_path = Path(input_data["project_path"])
        ioc_file = self._find_ioc_file(project_path)
        
        adc_configs = self._parse_ioc_for_adc(ioc_file)
        
        return {
            "adc_configurations": adc_configs
        }

    def _find_ioc_file(self, project_path: Path) -> Path:
        ioc_files = list(project_path.glob("**/*.ioc"))
        if not ioc_files:
            raise ValueError("未找到STM32CubeMX配置文件(.ioc)")
        return ioc_files[0]

    def _parse_ioc_for_adc(self, ioc_file: Path) -> List[Dict[str, Any]]:
        config = configparser.ConfigParser()
        config.read(ioc_file, encoding='utf-8')
        
        adc_instances = [s for s in config.sections() if s.startswith('ADC')]
        configs = []

        for instance in adc_instances:
            configs.append({
                "instance": instance,
                "resolution": config.get(instance, 'Resolution', fallback='12'),
                "continuous_conv_mode": config.get(instance, 'ContinuousConvMode', fallback='Disable') == 'Enable',
            })
        return configs

    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "project_path": {"type": "string"}
            },
            "required": ["project_path"]
        }

    def get_output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "adc_configurations": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "instance": {"type": "string"},
                            "resolution": {"type": "string"},
                            "continuous_conv_mode": {"type": "boolean"}
                        }
                    }
                }
            }
        }
