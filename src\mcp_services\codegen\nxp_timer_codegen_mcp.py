
# src/mcp_services/codegen/nxp_timer_codegen_mcp.py
from jinja2 import Template
from typing import Dict, Any

from ..base_mcp import BaseMCP

class NXPTimerCodegenMCP(BaseMCP):
    """NXP Timer代码生成MCP服务"""

    def __init__(self):
        super().__init__("nxp_timer_codegen_mcp")
        self.timer_mapping = {
            "TIM1": "TPM1",
            "TIM2": "TPM2",
        }

    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        stm32_timer_configs = input_data["timer_configurations"]
        
        generated_files = {}
        for timer_config in stm32_timer_configs:
            code_result = self._generate_timer_code(timer_config)
            instance_name = self.timer_mapping.get(timer_config["instance"], "TPM1")
            generated_files[f"{instance_name.lower()}_config.c"] = code_result["c_code"]
        
        return {
            "generated_files": generated_files,
            "header_includes": ["fsl_tpm.h", "fsl_clock.h"]
        }

    def _generate_timer_code(self, stm32_config: Dict) -> Dict[str, Any]:
        nxp_instance = self.timer_mapping.get(stm32_config["instance"], "TPM1")
        
        template = Template("""
/* {{nxp_instance}} initialization */
void {{nxp_instance}}_Init(void)
{
    tpm_config_t {{nxp_instance.lower()}}_config;

    TPM_GetDefaultConfig(&{{nxp_instance.lower()}}_config);
    {{nxp_instance.lower()}}_config.prescale = kTPM_Prescale_Divide_{{prescaler}};
    TPM_Init({{nxp_instance}}, &{{nxp_instance.lower()}}_config);
    TPM_SetTimerPeriod({{nxp_instance}}, USEC_TO_COUNT({{period_us}}, CLOCK_GetFreq(kCLOCK_ScgSysPllAsyncDiv2Clk)));
}
        """)
        
        code = template.render(
            nxp_instance=nxp_instance,
            prescaler=self._map_prescaler(stm32_config["prescaler"]),
            period_us=self._calculate_period_us(stm32_config["period"], stm32_config["prescaler"])
        )
        
        return {
            "c_code": code.strip()
        }

    def _map_prescaler(self, prescaler: int) -> int:
        # Simplified mapping
        if prescaler < 2:
            return 1
        if prescaler < 4:
            return 2
        if prescaler < 8:
            return 4
        if prescaler < 16:
            return 8
        if prescaler < 32:
            return 16
        if prescaler < 64:
            return 32
        if prescaler < 128:
            return 64
        return 128

    def _calculate_period_us(self, period: int, prescaler: int) -> int:
        # Assuming 84MHz clock
        return int((period * (prescaler + 1)) / 84)

    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "timer_configurations": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "instance": {"type": "string"},
                            "prescaler": {"type": "integer"},
                            "period": {"type": "integer"}
                        }
                    }
                }
            }
        }

    def get_output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "generated_files": {
                    "type": "object"
                },
                "header_includes": {
                    "type": "array"
                }
            }
        }
