#!/usr/bin/env python3
"""
MCP集成测试
测试MCP服务器和各个工具的功能
"""

import pytest
import json
import asyncio
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from interfaces.mcp.server import MCPServer

class TestMCPIntegration:
    """MCP集成测试类"""
    
    @pytest.fixture
    def mcp_server(self):
        """创建MCP服务器实例"""
        return MCPServer()
    
    @pytest.mark.asyncio
    async def test_initialize_request(self, mcp_server):
        """测试初始化请求"""
        request = {
            "jsonrpc": "2.0",
            "id": "1",
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        response = await mcp_server.handle_request(request)
        
        assert response["jsonrpc"] == "2.0"
        assert response["id"] == "1"
        assert "result" in response
        assert response["result"]["protocolVersion"] == "2024-11-05"
        assert "capabilities" in response["result"]
        assert "serverInfo" in response["result"]
    
    @pytest.mark.asyncio
    async def test_list_tools_request(self, mcp_server):
        """测试列出工具请求"""
        request = {
            "jsonrpc": "2.0",
            "id": "2",
            "method": "tools/list",
            "params": {}
        }
        
        response = await mcp_server.handle_request(request)
        
        assert response["jsonrpc"] == "2.0"
        assert response["id"] == "2"
        assert "result" in response
        assert "tools" in response["result"]
        
        tools = response["result"]["tools"]
        tool_names = [tool["name"] for tool in tools]
        
        # 验证预期的工具存在
        assert "analyze_stm32_pins" in tool_names
        assert "generate_nxp_uart" in tool_names
        assert "list_mcp_services" in tool_names
    
    @pytest.mark.asyncio
    async def test_analyze_pins_tool(self, mcp_server):
        """测试引脚分析工具"""
        # 创建测试项目目录
        test_project_path = Path(__file__).parent.parent / "examples" / "sample_stm32_project"
        
        request = {
            "jsonrpc": "2.0",
            "id": "3",
            "method": "tools/call",
            "params": {
                "name": "analyze_stm32_pins",
                "arguments": {
                    "project_path": str(test_project_path),
                    "deep_analysis": True
                }
            }
        }
        
        response = await mcp_server.handle_request(request)
        
        assert response["jsonrpc"] == "2.0"
        assert response["id"] == "3"
        
        if "result" in response:
            # 成功情况
            assert "content" in response["result"]
            content = response["result"]["content"][0]["text"]
            assert "STM32引脚配置分析结果" in content
            assert "项目信息" in content
        else:
            # 错误情况（可能是测试环境问题）
            assert "error" in response
            print(f"分析失败: {response['error']['message']}")
    
    @pytest.mark.asyncio
    async def test_generate_uart_tool(self, mcp_server):
        """测试UART代码生成工具"""
        request = {
            "jsonrpc": "2.0",
            "id": "4",
            "method": "tools/call",
            "params": {
                "name": "generate_nxp_uart",
                "arguments": {
                    "uart_configurations": [
                        {
                            "instance": "USART1",
                            "baudrate": 115200,
                            "data_bits": 8,
                            "stop_bits": 1,
                            "parity": "None"
                        }
                    ],
                    "target_mcu": "MCXN947"
                }
            }
        }
        
        response = await mcp_server.handle_request(request)
        
        assert response["jsonrpc"] == "2.0"
        assert response["id"] == "4"
        assert "result" in response
        assert "content" in response["result"]
        
        content = response["result"]["content"][0]["text"]
        assert "NXP UART代码生成结果" in content
        assert "LPUART1_Init" in content
        assert "fsl_lpuart.h" in content
    
    @pytest.mark.asyncio
    async def test_list_services_tool(self, mcp_server):
        """测试列出服务工具"""
        request = {
            "jsonrpc": "2.0",
            "id": "5",
            "method": "tools/call",
            "params": {
                "name": "list_mcp_services",
                "arguments": {}
            }
        }
        
        response = await mcp_server.handle_request(request)
        
        assert response["jsonrpc"] == "2.0"
        assert response["id"] == "5"
        assert "result" in response
        assert "content" in response["result"]
        
        content = response["result"]["content"][0]["text"]
        assert "可用的MCP服务" in content
        assert "analyze_pin_configuration_mcp" in content
        assert "nxp_uart_codegen_mcp" in content
    
    @pytest.mark.asyncio
    async def test_unknown_method(self, mcp_server):
        """测试未知方法"""
        request = {
            "jsonrpc": "2.0",
            "id": "6",
            "method": "unknown_method",
            "params": {}
        }
        
        response = await mcp_server.handle_request(request)
        
        assert response["jsonrpc"] == "2.0"
        assert response["id"] == "6"
        assert "error" in response
        assert response["error"]["code"] == -32601
        assert "Method not found" in response["error"]["message"]
    
    @pytest.mark.asyncio
    async def test_unknown_tool(self, mcp_server):
        """测试未知工具"""
        request = {
            "jsonrpc": "2.0",
            "id": "7",
            "method": "tools/call",
            "params": {
                "name": "unknown_tool",
                "arguments": {}
            }
        }
        
        response = await mcp_server.handle_request(request)
        
        assert response["jsonrpc"] == "2.0"
        assert response["id"] == "7"
        assert "error" in response
        assert response["error"]["code"] == -32602
        assert "Unknown tool" in response["error"]["message"]
    
    @pytest.mark.asyncio
    async def test_invalid_json_request(self, mcp_server):
        """测试无效JSON请求"""
        # 模拟无效JSON
        with patch('json.loads', side_effect=json.JSONDecodeError("test", "test", 0)):
            response = mcp_server._error_response(None, -32700, "Parse error")
            
            assert response["jsonrpc"] == "2.0"
            assert response["id"] is None
            assert "error" in response
            assert response["error"]["code"] == -32700
    
    def test_tool_schema_validation(self, mcp_server):
        """测试工具Schema验证"""
        tools = mcp_server.tools
        
        # 验证analyze_stm32_pins工具Schema
        analyze_tool = tools["analyze_stm32_pins"]
        assert "description" in analyze_tool
        assert "inputSchema" in analyze_tool
        
        input_schema = analyze_tool["inputSchema"]
        assert input_schema["type"] == "object"
        assert "properties" in input_schema
        assert "project_path" in input_schema["properties"]
        assert "required" in input_schema
        assert "project_path" in input_schema["required"]
        
        # 验证generate_nxp_uart工具Schema
        uart_tool = tools["generate_nxp_uart"]
        assert "description" in uart_tool
        assert "inputSchema" in uart_tool
        
        input_schema = uart_tool["inputSchema"]
        assert "uart_configurations" in input_schema["properties"]
        assert "uart_configurations" in input_schema["required"]
    
    def test_error_response_format(self, mcp_server):
        """测试错误响应格式"""
        error_response = mcp_server._error_response("test_id", -32603, "Test error")
        
        assert error_response["jsonrpc"] == "2.0"
        assert error_response["id"] == "test_id"
        assert "error" in error_response
        assert error_response["error"]["code"] == -32603
        assert error_response["error"]["message"] == "Test error"


class TestMCPServerStandalone:
    """MCP服务器独立测试"""
    
    def test_server_initialization(self):
        """测试服务器初始化"""
        server = MCPServer()
        
        # 验证工具注册
        assert len(server.tools) > 0
        assert "analyze_stm32_pins" in server.tools
        assert "generate_nxp_uart" in server.tools
        assert "list_mcp_services" in server.tools
    
    def test_format_pin_analysis_result(self):
        """测试引脚分析结果格式化"""
        server = MCPServer()
        
        test_data = {
            "total_pins": 10,
            "mcu_package": "LQFP100",
            "source_file": "test.ioc",
            "pin_analysis": {
                "gpio_pins": 5,
                "alternate_function_pins": 3,
                "analog_pins": 2,
                "peripheral_usage": {
                    "USART1": {
                        "type": "UART",
                        "count": 2,
                        "pins": ["PA9", "PA10"]
                    }
                }
            },
            "mapping_suggestions": {
                "critical_pins": [
                    {
                        "stm32_pin": "PA9",
                        "function": "USART1_TX",
                        "peripheral_type": "UART"
                    }
                ],
                "mapping_notes": ["测试注意事项"]
            }
        }
        
        result = server._format_pin_analysis_result(test_data)
        
        assert "STM32引脚配置分析结果" in result
        assert "总引脚数: 10" in result
        assert "MCU封装: LQFP100" in result
        assert "USART1 (UART): 2 个引脚" in result
        assert "PA9: USART1_TX (UART)" in result
        assert "测试注意事项" in result
    
    def test_format_uart_generation_result(self):
        """测试UART代码生成结果格式化"""
        server = MCPServer()
        
        test_data = {
            "uart_count": 1,
            "target_uart_type": "LPUART",
            "code_quality_metrics": {
                "total_files": 2,
                "total_lines": 100,
                "comment_ratio": 0.25
            },
            "generated_files": {
                "lpuart1_config.c": "// Test C code\nvoid test() {}"
            },
            "header_includes": ["fsl_lpuart.h", "fsl_clock.h"]
        }
        
        result = server._format_uart_generation_result(test_data)
        
        assert "NXP UART代码生成结果" in result
        assert "UART实例数: 1" in result
        assert "目标UART类型: LPUART" in result
        assert "注释率: 25.00%" in result
        assert "lpuart1_config.c" in result
        assert "```c" in result
        assert "fsl_lpuart.h" in result


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
