
# src/mcp_services/codegen/nxp_i2c_codegen_mcp.py
from jinja2 import Template
from typing import Dict, Any

from ..base_mcp import BaseMCP

class NXPI2CCodegenMCP(BaseMCP):
    """NXP I2C代码生成MCP服务"""

    def __init__(self):
        super().__init__("nxp_i2c_codegen_mcp")
        self.i2c_mapping = {
            "I2C1": "LPI2C1",
            "I2C2": "LPI2C2",
        }

    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        stm32_i2c_configs = input_data["i2c_configurations"]
        
        generated_files = {}
        for i2c_config in stm32_i2c_configs:
            code_result = self._generate_i2c_code(i2c_config)
            instance_name = self.i2c_mapping.get(i2c_config["instance"], "LPI2C1")
            generated_files[f"{instance_name.lower()}_config.c"] = code_result["c_code"]
        
        return {
            "generated_files": generated_files,
            "header_includes": ["fsl_lpi2c.h", "fsl_clock.h"]
        }

    def _generate_i2c_code(self, stm32_config: Dict) -> Dict[str, Any]:
        nxp_instance = self.i2c_mapping.get(stm32_config["instance"], "LPI2C1")
        
        template = Template("""
/* {{nxp_instance}} initialization */
void {{nxp_instance}}_Init(void)
{
    lpi2c_master_config_t {{nxp_instance.lower()}}_masterConfig;

    LPI2C_MasterGetDefaultConfig(&{{nxp_instance.lower()}}_masterConfig);
    {{nxp_instance.lower()}}_masterConfig.baudRate_Hz = {{timing_hz}};
    LPI2C_MasterInit({{nxp_instance}}, &{{nxp_instance.lower()}}_masterConfig, CLOCK_GetFreq(kCLOCK_ScgSysPllAsyncDiv2Clk));
}
        """)
        
        code = template.render(
            nxp_instance=nxp_instance,
            timing_hz=stm32_config["timing_hz"]
        )
        
        return {
            "c_code": code.strip()
        }

    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "i2c_configurations": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "instance": {"type": "string"},
                            "timing_hz": {"type": "integer"}
                        }
                    }
                }
            }
        }

    def get_output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "generated_files": {
                    "type": "object"
                },
                "header_includes": {
                    "type": "array"
                }
            }
        }
