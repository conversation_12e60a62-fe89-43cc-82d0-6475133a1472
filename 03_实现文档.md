# STM32到NXP MCU代码迁移工具 - 技术实现文档

## 1. 项目结构

### 1.1 目录结构
```
stm32-to-nxp-migration-tool/
├── src/
│   ├── mcp_services/           # MCP服务实现
│   │   ├── analysis/          # 分析类MCP服务
│   │   ├── codegen/           # 代码生成类MCP服务
│   │   └── utils/             # 工具类MCP服务
│   ├── core/                  # 核心组件
│   │   ├── orchestrator.py    # 任务编排器
│   │   ├── quality_gate.py    # 质量门禁
│   │   └── llm_pipeline.py    # LLM代码生成流水线
│   ├── interfaces/            # 用户接口
│   │   ├── cli/              # 命令行界面
│   │   ├── web/              # Web界面
│   │   └── vscode/           # VSCode插件
│   └── data/                  # 数据层
│       ├── schemas/          # JSON Schema定义
│       ├── templates/        # 代码模板
│       └── specs/            # MCU规格数据
├── tests/                     # 测试代码
├── docs/                      # 文档
├── examples/                  # 示例项目
└── scripts/                   # 构建和部署脚本
```

### 1.2 技术栈
- **后端**：Python 3.8+, FastAPI, SQLAlchemy
- **前端**：React, TypeScript, Ant Design
- **MCP协议**：基于JSON-RPC 2.0
- **LLM集成**：OpenAI API, Anthropic Claude API, Google Gemini API
- **代码分析**：Tree-sitter, Clang Static Analyzer
- **测试框架**：pytest, Ceedling (C单元测试)
- **部署**：Docker, Kubernetes

## 2. MCP服务实现

### 2.1 MCP服务基类

```python
# src/mcp_services/base_mcp.py
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import json
import time
from datetime import datetime
import jsonschema

class BaseMCP(ABC):
    """MCP服务基类，定义统一的接口规范"""
    
    def __init__(self, name: str, version: str = "1.0.0"):
        self.name = name
        self.version = version
        self.schema_validator = jsonschema.Draft7Validator
        
    @abstractmethod
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理输入数据，返回标准格式的结果"""
        pass
    
    @abstractmethod
    def get_input_schema(self) -> Dict[str, Any]:
        """返回输入数据的JSON Schema"""
        pass
    
    @abstractmethod
    def get_output_schema(self) -> Dict[str, Any]:
        """返回输出数据的JSON Schema"""
        pass
    
    def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行MCP服务，包含完整的错误处理和验证"""
        start_time = time.time()
        
        try:
            # 输入验证
            self._validate_input(input_data)
            
            # 执行处理逻辑
            result_data = self.process(input_data)
            
            # 输出验证
            self._validate_output(result_data)
            
            # 构建标准响应
            response = self._build_response(
                status="success",
                data=result_data,
                execution_time=time.time() - start_time
            )
            
        except ValidationError as e:
            response = self._build_response(
                status="error",
                error={
                    "code": "E_VALIDATION_FAILED",
                    "message": str(e),
                    "suggestion": "检查输入数据格式"
                },
                execution_time=time.time() - start_time
            )
        except Exception as e:
            response = self._build_response(
                status="error",
                error={
                    "code": "E_PROCESSING_FAILED",
                    "message": str(e),
                    "suggestion": "检查服务配置和输入数据"
                },
                execution_time=time.time() - start_time
            )
            
        return response
    
    def _validate_input(self, data: Dict[str, Any]):
        """验证输入数据"""
        schema = self.get_input_schema()
        jsonschema.validate(data, schema)
    
    def _validate_output(self, data: Dict[str, Any]):
        """验证输出数据"""
        schema = self.get_output_schema()
        jsonschema.validate(data, schema)
    
    def _build_response(self, status: str, data: Optional[Dict] = None, 
                       error: Optional[Dict] = None, execution_time: float = 0):
        """构建标准响应格式"""
        return {
            "mcp_name": self.name,
            "status": status,
            "version": self.version,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "execution_time_ms": int(execution_time * 1000),
            "data": data or {},
            "metadata": {
                "confidence_score": 0.95,  # 可由子类重写
                "data_quality": "high",
                "validation_passed": status == "success"
            },
            "error": error
        }
```

### 2.2 分析类MCP服务实现示例

```python
# src/mcp_services/analysis/analyze_pin_configuration_mcp.py
import configparser
import re
from pathlib import Path
from typing import Dict, Any, List

class AnalyzePinConfigurationMCP(BaseMCP):
    """引脚配置分析MCP服务"""
    
    def __init__(self):
        super().__init__("analyze_pin_configuration_mcp")
        
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析STM32项目的引脚配置"""
        project_path = Path(input_data["project_path"])
        
        # 查找.ioc文件
        ioc_files = list(project_path.glob("**/*.ioc"))
        if not ioc_files:
            raise ValueError("未找到STM32CubeMX配置文件(.ioc)")
        
        ioc_file = ioc_files[0]  # 使用第一个找到的.ioc文件
        
        # 解析引脚配置
        pin_configs = self._parse_ioc_file(ioc_file)
        
        return {
            "pin_configurations": pin_configs,
            "total_pins": len(pin_configs),
            "source_file": str(ioc_file.relative_to(project_path))
        }
    
    def _parse_ioc_file(self, ioc_file: Path) -> List[Dict[str, Any]]:
        """解析.ioc文件中的引脚配置"""
        config = configparser.ConfigParser()
        config.read(ioc_file, encoding='utf-8')
        
        pins = []
        
        # 遍历所有配置项
        for section_name in config.sections():
            for key, value in config[section_name].items():
                if self._is_pin_config(key):
                    pin_info = self._parse_pin_config(key, value)
                    if pin_info:
                        pins.append(pin_info)
        
        return pins
    
    def _is_pin_config(self, key: str) -> bool:
        """判断是否为引脚配置项"""
        pin_pattern = r'^P[A-Z]\d+\.'
        return bool(re.match(pin_pattern, key))
    
    def _parse_pin_config(self, key: str, value: str) -> Dict[str, Any]:
        """解析具体的引脚配置"""
        parts = key.split('.')
        pin_name = parts[0]  # 如 PA9
        attribute = parts[1] if len(parts) > 1 else None
        
        return {
            "pin_name": pin_name,
            "attribute": attribute,
            "value": value,
            "gpio_config": self._extract_gpio_config(attribute, value)
        }
    
    def _extract_gpio_config(self, attribute: str, value: str) -> Dict[str, Any]:
        """提取GPIO配置信息"""
        config = {}
        
        if attribute == "Signal":
            config["signal"] = value
        elif attribute == "Mode":
            config["mode"] = value
        elif attribute == "Pull":
            config["pull"] = value
        elif attribute == "Speed":
            config["speed"] = value
            
        return config
    
    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "project_path": {"type": "string"},
                "analysis_config": {
                    "type": "object",
                    "properties": {
                        "deep_analysis": {"type": "boolean"},
                        "include_generated_code": {"type": "boolean"}
                    }
                }
            },
            "required": ["project_path"]
        }
    
    def get_output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "pin_configurations": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "pin_name": {"type": "string"},
                            "attribute": {"type": "string"},
                            "value": {"type": "string"},
                            "gpio_config": {"type": "object"}
                        }
                    }
                },
                "total_pins": {"type": "integer"},
                "source_file": {"type": "string"}
            }
        }
```

### 2.3 代码生成类MCP服务实现示例

```python
# src/mcp_services/codegen/nxp_uart_codegen_mcp.py
from jinja2 import Template
from typing import Dict, Any

class NXPUARTCodegenMCP(BaseMCP):
    """NXP UART代码生成MCP服务"""
    
    def __init__(self):
        super().__init__("nxp_uart_codegen_mcp")
        self.uart_mapping = {
            "USART1": "LPUART1",
            "USART2": "LPUART2",
            "USART3": "LPUART3"
        }
        
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成NXP UART初始化代码"""
        stm32_uart_configs = input_data["uart_configurations"]
        target_mcu = input_data.get("target_mcu", "MCXN947")
        
        generated_files = {}
        
        for uart_config in stm32_uart_configs:
            code_result = self._generate_uart_code(uart_config, target_mcu)
            instance_name = code_result["instance_mapping"]["nxp"]
            generated_files[f"{instance_name.lower()}_config.c"] = code_result["c_code"]
        
        return {
            "generated_files": generated_files,
            "header_includes": ["fsl_lpuart.h", "fsl_clock.h"],
            "uart_count": len(stm32_uart_configs)
        }
    
    def _generate_uart_code(self, stm32_config: Dict, target_mcu: str) -> Dict[str, Any]:
        """生成单个UART的初始化代码"""
        stm32_instance = stm32_config["instance"]
        nxp_instance = self.uart_mapping.get(stm32_instance, "LPUART1")
        
        # 使用Jinja2模板生成代码
        template = Template("""
/* {{nxp_instance}} initialization */
void {{nxp_instance}}_Init(void)
{
    lpuart_config_t {{nxp_instance.lower()}}_config;
    
    /* Get default configuration */
    LPUART_GetDefaultConfig(&{{nxp_instance.lower()}}_config);
    
    /* Configure UART parameters */
    {{nxp_instance.lower()}}_config.baudRate_Bps = {{baudrate}};
    {{nxp_instance.lower()}}_config.dataBitsCount = {{data_bits_enum}};
    {{nxp_instance.lower()}}_config.stopBitCount = {{stop_bits_enum}};
    {{nxp_instance.lower()}}_config.parityMode = {{parity_enum}};
    {{nxp_instance.lower()}}_config.enableTx = true;
    {{nxp_instance.lower()}}_config.enableRx = true;
    
    /* Initialize UART */
    LPUART_Init({{nxp_instance}}, &{{nxp_instance.lower()}}_config, 
                CLOCK_GetFreq(kCLOCK_ScgSysPllAsyncDiv2Clk));
}
        """)
        
        code = template.render(
            nxp_instance=nxp_instance,
            baudrate=stm32_config["baudrate"],
            data_bits_enum=self._map_data_bits(stm32_config["data_bits"]),
            stop_bits_enum=self._map_stop_bits(stm32_config["stop_bits"]),
            parity_enum=self._map_parity(stm32_config["parity"])
        )
        
        return {
            "c_code": code.strip(),
            "instance_mapping": {
                "stm32": stm32_instance,
                "nxp": nxp_instance
            }
        }
    
    def _map_data_bits(self, stm32_data_bits: int) -> str:
        """映射数据位设置"""
        mapping = {
            8: "kLPUART_EightDataBits",
            9: "kLPUART_NineDataBits"
        }
        return mapping.get(stm32_data_bits, "kLPUART_EightDataBits")
    
    def _map_stop_bits(self, stm32_stop_bits: int) -> str:
        """映射停止位设置"""
        mapping = {
            1: "kLPUART_OneStopBit",
            2: "kLPUART_TwoStopBit"
        }
        return mapping.get(stm32_stop_bits, "kLPUART_OneStopBit")
    
    def _map_parity(self, stm32_parity: str) -> str:
        """映射奇偶校验设置"""
        mapping = {
            "None": "kLPUART_ParityDisabled",
            "Even": "kLPUART_ParityEven",
            "Odd": "kLPUART_ParityOdd"
        }
        return mapping.get(stm32_parity, "kLPUART_ParityDisabled")
    
    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "uart_configurations": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "instance": {"type": "string"},
                            "baudrate": {"type": "integer"},
                            "data_bits": {"type": "integer"},
                            "stop_bits": {"type": "integer"},
                            "parity": {"type": "string"}
                        }
                    }
                },
                "target_mcu": {"type": "string"}
            },
            "required": ["uart_configurations"]
        }
    
    def get_output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "generated_files": {
                    "type": "object",
                    "patternProperties": {
                        ".*\\.c$": {"type": "string"}
                    }
                },
                "header_includes": {
                    "type": "array",
                    "items": {"type": "string"}
                },
                "uart_count": {"type": "integer"}
            }
        }
```

## 3. 核心组件实现

### 3.1 任务编排器实现

```python
# src/core/orchestrator.py
import asyncio
from typing import Dict, Any, List
from enum import Enum
import json
from pathlib import Path

class OrchestratorState(Enum):
    IDLE = "idle"
    ANALYZING = "analyzing"
    VALIDATING_CONTEXT = "validating_context"
    GENERATING = "generating"
    STATIC_ANALYZING = "static_analyzing"
    COMPILING = "compiling"
    UNIT_TESTING = "unit_testing"
    ASSESSING_QUALITY = "assessing_quality"
    PACKAGING = "packaging"
    DONE = "done"
    ERROR = "error"
    PAUSED_FOR_REVIEW = "paused_for_review"

class MigrationOrchestrator:
    """迁移任务编排器"""
    
    def __init__(self, config_path: str):
        self.state = OrchestratorState.IDLE
        self.config = self._load_config(config_path)
        self.dependency_graph = self.config["dependency_graph"]
        self.mcp_services = {}
        self.execution_log = []
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    async def execute_migration(self, project_path: str, target_mcu: str) -> Dict[str, Any]:
        """执行完整的迁移流程"""
        try:
            self.state = OrchestratorState.ANALYZING
            analysis_results = await self._execute_analysis_phase(project_path)
            
            self.state = OrchestratorState.VALIDATING_CONTEXT
            validation_result = await self._validate_analysis_context(analysis_results)
            if not validation_result["valid"]:
                raise ValueError(f"Context validation failed: {validation_result['errors']}")
            
            self.state = OrchestratorState.GENERATING
            generation_results = await self._execute_generation_phase(analysis_results, target_mcu)
            
            self.state = OrchestratorState.STATIC_ANALYZING
            static_analysis_results = await self._execute_static_analysis(generation_results)
            
            self.state = OrchestratorState.COMPILING
            compile_results = await self._execute_compilation(generation_results)
            
            self.state = OrchestratorState.UNIT_TESTING
            test_results = await self._execute_unit_testing(generation_results)
            
            self.state = OrchestratorState.ASSESSING_QUALITY
            quality_results = await self._assess_quality(
                static_analysis_results, compile_results, test_results
            )
            
            self.state = OrchestratorState.PACKAGING
            final_package = await self._package_deliverables(generation_results, quality_results)
            
            self.state = OrchestratorState.DONE
            return final_package
            
        except Exception as e:
            self.state = OrchestratorState.ERROR
            self._log_error(str(e))
            raise
    
    async def _execute_analysis_phase(self, project_path: str) -> Dict[str, Any]:
        """执行分析阶段"""
        analysis_results = {}
        
        # 根据依赖图确定执行顺序
        execution_order = self._resolve_dependencies("analysis")
        
        for mcp_name in execution_order:
            if mcp_name.startswith("analyze_"):
                mcp_service = self._get_mcp_service(mcp_name)
                input_data = {
                    "project_path": project_path,
                    "analysis_config": self.config.get("analysis_config", {}),
                    "context": analysis_results
                }
                
                result = await self._execute_mcp_service(mcp_service, input_data)
                analysis_results[mcp_name] = result
                
        return analysis_results
    
    async def _execute_generation_phase(self, analysis_results: Dict, target_mcu: str) -> Dict[str, Any]:
        """执行代码生成阶段"""
        generation_results = {}
        
        # 根据依赖图确定执行顺序
        execution_order = self._resolve_dependencies("generation")
        
        for mcp_name in execution_order:
            if mcp_name.endswith("_codegen_mcp"):
                mcp_service = self._get_mcp_service(mcp_name)
                
                # 构建输入数据
                input_data = self._build_codegen_input(mcp_name, analysis_results, target_mcu)
                
                result = await self._execute_mcp_service(mcp_service, input_data)
                generation_results[mcp_name] = result
                
        return generation_results
    
    def _resolve_dependencies(self, phase: str) -> List[str]:
        """解析依赖关系，返回执行顺序"""
        # 简化的拓扑排序实现
        visited = set()
        result = []
        
        def dfs(node):
            if node in visited:
                return
            visited.add(node)
            
            for dependency in self.dependency_graph.get(node, []):
                dfs(dependency)
            
            if (phase == "analysis" and node.startswith("analyze_")) or \
               (phase == "generation" and node.endswith("_codegen_mcp")):
                result.append(node)
        
        for node in self.dependency_graph:
            dfs(node)
            
        return result
    
    def _get_mcp_service(self, mcp_name: str):
        """获取MCP服务实例"""
        if mcp_name not in self.mcp_services:
            # 动态加载MCP服务
            module_path = f"src.mcp_services.{self._get_mcp_category(mcp_name)}.{mcp_name}"
            module = __import__(module_path, fromlist=[mcp_name])
            service_class = getattr(module, self._get_service_class_name(mcp_name))
            self.mcp_services[mcp_name] = service_class()
            
        return self.mcp_services[mcp_name]
    
    async def _execute_mcp_service(self, service, input_data: Dict) -> Dict[str, Any]:
        """执行MCP服务"""
        # 在实际实现中，这里应该是异步调用
        result = service.execute(input_data)
        self._log_execution(service.name, result["status"], result["execution_time_ms"])
        return result
    
    def _log_execution(self, service_name: str, status: str, execution_time: int):
        """记录执行日志"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "service": service_name,
            "status": status,
            "execution_time_ms": execution_time
        }
        self.execution_log.append(log_entry)
```

## 4. 部署与运行

### 4.1 Docker部署

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    cmake \
    git \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制源代码
COPY src/ ./src/
COPY tests/ ./tests/
COPY scripts/ ./scripts/

# 设置环境变量
ENV PYTHONPATH=/app/src

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "src.interfaces.web.main"]
```

### 4.2 配置文件

```yaml
# config/production.yaml
system:
  log_level: INFO
  max_concurrent_projects: 10
  temp_directory: /tmp/migration_tool

mcp_services:
  timeout_seconds: 300
  retry_attempts: 3
  
llm_providers:
  openai:
    api_key: ${OPENAI_API_KEY}
    model: gpt-4
    max_tokens: 4000
  anthropic:
    api_key: ${ANTHROPIC_API_KEY}
    model: claude-3-sonnet-20240229
    max_tokens: 4000

quality_gates:
  min_line_coverage: 85
  min_branch_coverage: 75
  max_cyclomatic_complexity: 10
  static_analysis_errors: 0
  compilation_warnings: 0
  misra_c_violations: 0

database:
  url: postgresql://user:password@localhost:5432/migration_tool
  pool_size: 10
  max_overflow: 20
```

### 4.3 CLI使用示例

```bash
# 安装工具
pip install stm32-to-nxp-migration-tool

# 分析STM32项目
migration-tool analyze /path/to/stm32_project --output analysis_result.json

# 执行完整迁移
migration-tool migrate /path/to/stm32_project \
  --target-mcu MCXN947 \
  --output-dir /path/to/output \
  --quality-profile strict

# 在VSCode中使用MCP服务
# 在VSCode的settings.json中配置：
{
  "mcp.servers": {
    "stm32-migration": {
      "command": "python",
      "args": ["-m", "src.interfaces.mcp.server"],
      "cwd": "/path/to/migration-tool"
    }
  }
}
```

## 5. 测试策略

### 5.1 单元测试

```python
# tests/test_pin_analysis_mcp.py
import pytest
from src.mcp_services.analysis.analyze_pin_configuration_mcp import AnalyzePinConfigurationMCP

class TestAnalyzePinConfigurationMCP:
    
    def setup_method(self):
        self.mcp = AnalyzePinConfigurationMCP()
    
    def test_process_valid_project(self):
        input_data = {
            "project_path": "tests/fixtures/sample_stm32_project"
        }
        
        result = self.mcp.execute(input_data)
        
        assert result["status"] == "success"
        assert "pin_configurations" in result["data"]
        assert result["data"]["total_pins"] > 0
    
    def test_process_missing_ioc_file(self):
        input_data = {
            "project_path": "tests/fixtures/empty_project"
        }
        
        result = self.mcp.execute(input_data)
        
        assert result["status"] == "error"
        assert "未找到STM32CubeMX配置文件" in result["error"]["message"]
```

### 5.2 集成测试

```python
# tests/test_integration.py
import pytest
from src.core.orchestrator import MigrationOrchestrator

class TestMigrationIntegration:
    
    def setup_method(self):
        self.orchestrator = MigrationOrchestrator("config/test.json")
    
    @pytest.mark.asyncio
    async def test_complete_migration_flow(self):
        project_path = "tests/fixtures/complete_stm32_project"
        target_mcu = "MCXN947"
        
        result = await self.orchestrator.execute_migration(project_path, target_mcu)
        
        assert result["status"] == "success"
        assert "generated_files" in result
        assert "quality_report" in result
```

## 6. 监控与维护

### 6.1 日志配置

```python
# src/utils/logging_config.py
import logging
import json
from datetime import datetime

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        if hasattr(record, 'mcp_name'):
            log_entry["mcp_name"] = record.mcp_name
        if hasattr(record, 'execution_time'):
            log_entry["execution_time_ms"] = record.execution_time
            
        return json.dumps(log_entry)

def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('migration_tool.log')
        ]
    )
    
    # 为所有处理器设置JSON格式
    for handler in logging.root.handlers:
        handler.setFormatter(JSONFormatter())
```

### 6.2 性能监控

```python
# src/utils/metrics.py
import time
from functools import wraps
from typing import Dict, Any
import psutil

class PerformanceMonitor:
    def __init__(self):
        self.metrics = {}
    
    def measure_execution_time(self, func_name: str):
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                start_memory = psutil.Process().memory_info().rss
                
                try:
                    result = func(*args, **kwargs)
                    status = "success"
                except Exception as e:
                    status = "error"
                    raise
                finally:
                    end_time = time.time()
                    end_memory = psutil.Process().memory_info().rss
                    
                    self.metrics[func_name] = {
                        "execution_time_ms": (end_time - start_time) * 1000,
                        "memory_usage_mb": (end_memory - start_memory) / 1024 / 1024,
                        "status": status,
                        "timestamp": time.time()
                    }
                
                return result
            return wrapper
        return decorator
    
    def get_metrics(self) -> Dict[str, Any]:
        return self.metrics.copy()
```

## 7. 关键MCP服务完整实现

### 7.1 MCU匹配服务实现

```python
# src/mcp_services/analysis/find_nxp_equivalent_mcp.py
import json
from pathlib import Path
from typing import Dict, Any, List
from dataclasses import dataclass

@dataclass
class MCUSpec:
    part_number: str
    core: str
    flash_kb: int
    ram_kb: int
    package: str
    peripherals: Dict[str, int]
    frequency_mhz: int

class FindNXPEquivalentMCP(BaseMCP):
    """查找NXP等效MCU的MCP服务"""

    def __init__(self):
        super().__init__("find_nxp_equivalent_mcp")
        self.mcu_database = self._load_mcu_database()

    def _load_mcu_database(self) -> Dict[str, List[MCUSpec]]:
        """加载MCU规格数据库"""
        db_path = Path("src/data/specs/mcu_database.json")
        with open(db_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        database = {}
        for vendor, mcus in data.items():
            database[vendor] = [MCUSpec(**mcu) for mcu in mcus]

        return database

    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """查找匹配的NXP MCU"""
        stm32_part = input_data["stm32_part_number"]
        target_family = input_data.get("target_nxp_family", "all")

        # 获取STM32规格
        stm32_spec = self._get_stm32_spec(stm32_part)
        if not stm32_spec:
            raise ValueError(f"未找到STM32 MCU规格: {stm32_part}")

        # 查找匹配的NXP MCU
        nxp_candidates = self._find_matching_nxp_mcus(stm32_spec, target_family)

        # 计算匹配分数并排序
        scored_candidates = self._score_candidates(stm32_spec, nxp_candidates)

        return {
            "source_stm32_part": stm32_part,
            "stm32_specifications": self._spec_to_dict(stm32_spec),
            "recommended_nxp_parts": scored_candidates[:5],  # 返回前5个最佳匹配
            "total_candidates": len(nxp_candidates)
        }

    def _get_stm32_spec(self, part_number: str) -> MCUSpec:
        """获取STM32 MCU规格"""
        stm32_mcus = self.mcu_database.get("STM32", [])
        for mcu in stm32_mcus:
            if mcu.part_number == part_number:
                return mcu
        return None

    def _find_matching_nxp_mcus(self, stm32_spec: MCUSpec, target_family: str) -> List[MCUSpec]:
        """查找匹配的NXP MCU"""
        nxp_mcus = self.mcu_database.get("NXP", [])

        candidates = []
        for nxp_mcu in nxp_mcus:
            # 基本筛选条件
            if target_family != "all" and target_family not in nxp_mcu.part_number:
                continue

            # 内存容量筛选（允许20%的差异）
            flash_ratio = nxp_mcu.flash_kb / stm32_spec.flash_kb
            ram_ratio = nxp_mcu.ram_kb / stm32_spec.ram_kb

            if 0.8 <= flash_ratio <= 1.5 and 0.8 <= ram_ratio <= 1.5:
                candidates.append(nxp_mcu)

        return candidates

    def _score_candidates(self, stm32_spec: MCUSpec, candidates: List[MCUSpec]) -> List[Dict]:
        """为候选MCU计算匹配分数"""
        scored_candidates = []

        for candidate in candidates:
            score = self._calculate_match_score(stm32_spec, candidate)
            scored_candidates.append({
                "part_number": candidate.part_number,
                "match_score": score,
                "core": candidate.core,
                "flash_kb": candidate.flash_kb,
                "ram_kb": candidate.ram_kb,
                "package": candidate.package,
                "frequency_mhz": candidate.frequency_mhz,
                "peripheral_compatibility": self._compare_peripherals(
                    stm32_spec.peripherals, candidate.peripherals
                ),
                "advantages": self._identify_advantages(stm32_spec, candidate),
                "limitations": self._identify_limitations(stm32_spec, candidate)
            })

        # 按匹配分数排序
        scored_candidates.sort(key=lambda x: x["match_score"], reverse=True)
        return scored_candidates

    def _calculate_match_score(self, stm32: MCUSpec, nxp: MCUSpec) -> float:
        """计算匹配分数（0-1之间）"""
        score = 0.0

        # 内存匹配分数 (40%权重)
        flash_score = min(nxp.flash_kb / stm32.flash_kb, 1.0)
        ram_score = min(nxp.ram_kb / stm32.ram_kb, 1.0)
        memory_score = (flash_score + ram_score) / 2
        score += memory_score * 0.4

        # 外设匹配分数 (35%权重)
        peripheral_score = self._calculate_peripheral_score(stm32.peripherals, nxp.peripherals)
        score += peripheral_score * 0.35

        # 性能匹配分数 (15%权重)
        freq_score = min(nxp.frequency_mhz / stm32.frequency_mhz, 1.0)
        score += freq_score * 0.15

        # 封装匹配分数 (10%权重)
        package_score = 1.0 if stm32.package == nxp.package else 0.7
        score += package_score * 0.1

        return round(score, 3)

    def _calculate_peripheral_score(self, stm32_peripherals: Dict, nxp_peripherals: Dict) -> float:
        """计算外设匹配分数"""
        if not stm32_peripherals:
            return 1.0

        total_score = 0.0
        peripheral_count = 0

        for peripheral, stm32_count in stm32_peripherals.items():
            if peripheral in nxp_peripherals:
                nxp_count = nxp_peripherals[peripheral]
                # 计算该外设的匹配分数
                peripheral_score = min(nxp_count / stm32_count, 1.0)
                total_score += peripheral_score
            else:
                # NXP没有该外设，分数为0
                total_score += 0.0
            peripheral_count += 1

        return total_score / peripheral_count if peripheral_count > 0 else 1.0

    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "stm32_part_number": {"type": "string"},
                "target_nxp_family": {
                    "type": "string",
                    "enum": ["all", "LPC", "Kinetis", "i.MX RT", "MCXN"]
                },
                "requirements": {
                    "type": "object",
                    "properties": {
                        "min_flash_kb": {"type": "integer"},
                        "min_ram_kb": {"type": "integer"},
                        "required_peripherals": {"type": "array", "items": {"type": "string"}}
                    }
                }
            },
            "required": ["stm32_part_number"]
        }
```

### 7.2 时钟树分析服务实现

```python
# src/mcp_services/analysis/analyze_clock_tree_mcp.py
import configparser
import re
from typing import Dict, Any, Optional

class AnalyzeClockTreeMCP(BaseMCP):
    """时钟树配置分析MCP服务"""

    def __init__(self):
        super().__init__("analyze_clock_tree_mcp")

    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析STM32项目的时钟树配置"""
        project_path = Path(input_data["project_path"])

        # 查找.ioc文件
        ioc_files = list(project_path.glob("**/*.ioc"))
        if not ioc_files:
            raise ValueError("未找到STM32CubeMX配置文件(.ioc)")

        ioc_file = ioc_files[0]

        # 解析时钟配置
        clock_config = self._parse_clock_configuration(ioc_file)

        # 计算派生时钟频率
        calculated_frequencies = self._calculate_derived_clocks(clock_config)

        return {
            "clock_configuration": clock_config,
            "calculated_frequencies": calculated_frequencies,
            "source_file": str(ioc_file.relative_to(project_path)),
            "analysis_summary": self._generate_analysis_summary(clock_config)
        }

    def _parse_clock_configuration(self, ioc_file: Path) -> Dict[str, Any]:
        """解析.ioc文件中的时钟配置"""
        config = configparser.ConfigParser()
        config.read(ioc_file, encoding='utf-8')

        clock_config = {
            "oscillators": self._parse_oscillators(config),
            "pll_config": self._parse_pll_config(config),
            "system_clocks": self._parse_system_clocks(config),
            "peripheral_clocks": self._parse_peripheral_clocks(config)
        }

        return clock_config

    def _parse_oscillators(self, config: configparser.ConfigParser) -> Dict[str, Any]:
        """解析振荡器配置"""
        oscillators = {}

        # HSE配置
        hse_state = config.get('RCC', 'RCC.HSEState', fallback='RCC_HSE_OFF')
        if 'ON' in hse_state or 'BYPASS' in hse_state:
            hse_freq = int(config.get('RCC', 'RCC.HSE_VALUE', fallback='8000000'))
            oscillators['hse'] = {
                'state': hse_state,
                'frequency_hz': hse_freq,
                'enabled': True
            }

        # HSI配置
        hsi_state = config.get('RCC', 'RCC.HSIState', fallback='RCC_HSI_ON')
        if 'ON' in hsi_state:
            oscillators['hsi'] = {
                'state': hsi_state,
                'frequency_hz': 16000000,  # HSI固定16MHz
                'enabled': True
            }

        # LSE配置
        lse_state = config.get('RCC', 'RCC.LSEState', fallback='RCC_LSE_OFF')
        if 'ON' in lse_state:
            oscillators['lse'] = {
                'state': lse_state,
                'frequency_hz': 32768,
                'enabled': True
            }

        # LSI配置
        lsi_state = config.get('RCC', 'RCC.LSIState', fallback='RCC_LSI_OFF')
        if 'ON' in lsi_state:
            oscillators['lsi'] = {
                'state': lsi_state,
                'frequency_hz': 32000,  # LSI约32kHz
                'enabled': True
            }

        return oscillators

    def _parse_pll_config(self, config: configparser.ConfigParser) -> Dict[str, Any]:
        """解析PLL配置"""
        pll_config = {}

        pll_state = config.get('RCC', 'RCC.PLLState', fallback='RCC_PLL_NONE')
        if 'ON' in pll_state:
            pll_config = {
                'enabled': True,
                'source': config.get('RCC', 'RCC.PLLSource', fallback='RCC_PLLSOURCE_HSI'),
                'pllm': int(config.get('RCC', 'RCC.PLLM', fallback='8')),
                'plln': int(config.get('RCC', 'RCC.PLLN', fallback='336')),
                'pllp': int(config.get('RCC', 'RCC.PLLP', fallback='2')),
                'pllq': int(config.get('RCC', 'RCC.PLLQ', fallback='7')),
            }

            # 计算PLL输出频率
            if 'HSE' in pll_config['source']:
                input_freq = 8000000  # 默认HSE频率
            else:
                input_freq = 16000000  # HSI频率

            vco_freq = (input_freq / pll_config['pllm']) * pll_config['plln']
            pll_config['vco_frequency_hz'] = vco_freq
            pll_config['pll_output_hz'] = vco_freq / pll_config['pllp']
        else:
            pll_config = {'enabled': False}

        return pll_config

    def _parse_system_clocks(self, config: configparser.ConfigParser) -> Dict[str, Any]:
        """解析系统时钟配置"""
        system_clocks = {}

        # SYSCLK源
        sysclk_source = config.get('RCC', 'RCC.SYSCLKSource', fallback='RCC_SYSCLKSOURCE_HSI')
        system_clocks['sysclk_source'] = sysclk_source

        # AHB分频器
        ahb_prescaler = config.get('RCC', 'RCC.AHBCLKDivider', fallback='RCC_SYSCLK_DIV1')
        system_clocks['ahb_prescaler'] = ahb_prescaler

        # APB1分频器
        apb1_prescaler = config.get('RCC', 'RCC.APB1CLKDivider', fallback='RCC_HCLK_DIV1')
        system_clocks['apb1_prescaler'] = apb1_prescaler

        # APB2分频器
        apb2_prescaler = config.get('RCC', 'RCC.APB2CLKDivider', fallback='RCC_HCLK_DIV1')
        system_clocks['apb2_prescaler'] = apb2_prescaler

        return system_clocks

    def _calculate_derived_clocks(self, clock_config: Dict[str, Any]) -> Dict[str, int]:
        """计算派生时钟频率"""
        frequencies = {}

        # 确定SYSCLK频率
        sysclk_source = clock_config['system_clocks']['sysclk_source']
        if 'PLL' in sysclk_source and clock_config['pll_config']['enabled']:
            sysclk_hz = clock_config['pll_config']['pll_output_hz']
        elif 'HSE' in sysclk_source and 'hse' in clock_config['oscillators']:
            sysclk_hz = clock_config['oscillators']['hse']['frequency_hz']
        else:
            sysclk_hz = 16000000  # HSI默认频率

        frequencies['sysclk_hz'] = sysclk_hz

        # 计算HCLK (AHB时钟)
        ahb_div = self._parse_prescaler(clock_config['system_clocks']['ahb_prescaler'])
        frequencies['hclk_hz'] = sysclk_hz // ahb_div

        # 计算PCLK1 (APB1时钟)
        apb1_div = self._parse_prescaler(clock_config['system_clocks']['apb1_prescaler'])
        frequencies['pclk1_hz'] = frequencies['hclk_hz'] // apb1_div

        # 计算PCLK2 (APB2时钟)
        apb2_div = self._parse_prescaler(clock_config['system_clocks']['apb2_prescaler'])
        frequencies['pclk2_hz'] = frequencies['hclk_hz'] // apb2_div

        # 计算外设时钟
        frequencies.update(self._calculate_peripheral_clocks(frequencies, clock_config))

        return frequencies

    def _parse_prescaler(self, prescaler_str: str) -> int:
        """解析分频器字符串，返回分频系数"""
        prescaler_map = {
            'RCC_SYSCLK_DIV1': 1, 'RCC_HCLK_DIV1': 1,
            'RCC_SYSCLK_DIV2': 2, 'RCC_HCLK_DIV2': 2,
            'RCC_SYSCLK_DIV4': 4, 'RCC_HCLK_DIV4': 4,
            'RCC_SYSCLK_DIV8': 8, 'RCC_HCLK_DIV8': 8,
            'RCC_SYSCLK_DIV16': 16, 'RCC_HCLK_DIV16': 16,
        }
        return prescaler_map.get(prescaler_str, 1)

    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "project_path": {"type": "string"},
                "analysis_config": {
                    "type": "object",
                    "properties": {
                        "include_peripheral_clocks": {"type": "boolean"},
                        "validate_frequencies": {"type": "boolean"}
                    }
                }
            },
            "required": ["project_path"]
        }
```

### 7.3 RTOS使用分析服务实现

```python
# src/mcp_services/analysis/analyze_rtos_usage_mcp.py
import re
from pathlib import Path
from typing import Dict, Any, List, Optional

class AnalyzeRTOSUsageMCP(BaseMCP):
    """RTOS使用情况分析MCP服务"""

    def __init__(self):
        super().__init__("analyze_rtos_usage_mcp")
        self.rtos_signatures = {
            "FreeRTOS": {
                "task_apis": ["xTaskCreate", "vTaskDelete", "vTaskDelay", "vTaskDelayUntil"],
                "queue_apis": ["xQueueCreate", "xQueueSend", "xQueueReceive"],
                "semaphore_apis": ["xSemaphoreCreate", "xSemaphoreGive", "xSemaphoreTake"],
                "mutex_apis": ["xMutexCreate", "xMutexGive", "xMutexTake"],
                "timer_apis": ["xTimerCreate", "xTimerStart", "xTimerStop"],
                "event_apis": ["xEventGroupCreate", "xEventGroupSetBits"],
                "headers": ["FreeRTOS.h", "task.h", "queue.h", "semphr.h", "timers.h", "event_groups.h"],
                "config_files": ["FreeRTOSConfig.h"]
            },
            "ThreadX": {
                "task_apis": ["tx_thread_create", "tx_thread_delete", "tx_thread_sleep"],
                "queue_apis": ["tx_queue_create", "tx_queue_send", "tx_queue_receive"],
                "semaphore_apis": ["tx_semaphore_create", "tx_semaphore_put", "tx_semaphore_get"],
                "mutex_apis": ["tx_mutex_create", "tx_mutex_put", "tx_mutex_get"],
                "timer_apis": ["tx_timer_create", "tx_timer_activate", "tx_timer_deactivate"],
                "headers": ["tx_api.h"],
                "config_files": ["tx_user.h"]
            }
        }

    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析项目中的RTOS使用情况"""
        project_path = Path(input_data["project_path"])

        # 检测RTOS类型
        rtos_detection = self._detect_rtos_type(project_path)

        if rtos_detection["type"] == "None":
            return {
                "rtos_type": "None",
                "confidence": 0.0,
                "analysis_summary": "项目中未检测到RTOS使用"
            }

        # 深度分析RTOS配置
        rtos_analysis = self._analyze_rtos_configuration(project_path, rtos_detection["type"])

        return {
            "rtos_type": rtos_detection["type"],
            "confidence": rtos_detection["confidence"],
            "detected_apis": rtos_detection["detected_apis"],
            "tasks": rtos_analysis["tasks"],
            "queues": rtos_analysis["queues"],
            "semaphores": rtos_analysis["semaphores"],
            "timers": rtos_analysis["timers"],
            "config_analysis": rtos_analysis["config"],
            "memory_management": rtos_analysis["memory"],
            "analysis_summary": self._generate_analysis_summary(rtos_analysis)
        }

    def _detect_rtos_type(self, project_path: Path) -> Dict[str, Any]:
        """检测RTOS类型"""
        detection_results = {}

        for rtos_type, signatures in self.rtos_signatures.items():
            detected_apis = []
            api_count = 0
            total_apis = 0

            # 统计所有API
            for api_category in ["task_apis", "queue_apis", "semaphore_apis", "mutex_apis", "timer_apis"]:
                total_apis += len(signatures[api_category])

            # 扫描源文件
            for c_file in project_path.glob("**/*.c"):
                try:
                    with open(c_file, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                    for api_category, apis in signatures.items():
                        if api_category.endswith("_apis"):
                            for api in apis:
                                if api in content:
                                    detected_apis.append(api)
                                    api_count += 1
                except Exception:
                    continue

            # 检查头文件
            header_found = False
            for header in signatures["headers"]:
                if list(project_path.glob(f"**/{header}")):
                    header_found = True
                    break

            # 计算置信度
            confidence = 0.0
            if api_count > 0:
                confidence = api_count / total_apis
                if header_found:
                    confidence = min(confidence + 0.2, 1.0)

            detection_results[rtos_type] = {
                "confidence": confidence,
                "detected_apis": list(set(detected_apis)),
                "api_count": api_count
            }

        # 选择置信度最高的RTOS
        best_match = max(detection_results.items(), key=lambda x: x[1]["confidence"])

        if best_match[1]["confidence"] > 0.3:
            return {
                "type": best_match[0],
                "confidence": best_match[1]["confidence"],
                "detected_apis": best_match[1]["detected_apis"]
            }
        else:
            return {"type": "None", "confidence": 0.0, "detected_apis": []}

    def _analyze_rtos_configuration(self, project_path: Path, rtos_type: str) -> Dict[str, Any]:
        """深度分析RTOS配置"""
        analysis = {
            "tasks": [],
            "queues": [],
            "semaphores": [],
            "timers": [],
            "config": {},
            "memory": {}
        }

        if rtos_type == "FreeRTOS":
            analysis = self._analyze_freertos_config(project_path)
        elif rtos_type == "ThreadX":
            analysis = self._analyze_threadx_config(project_path)

        return analysis

    def _analyze_freertos_config(self, project_path: Path) -> Dict[str, Any]:
        """分析FreeRTOS配置"""
        analysis = {
            "tasks": self._extract_freertos_tasks(project_path),
            "queues": self._extract_freertos_queues(project_path),
            "semaphores": self._extract_freertos_semaphores(project_path),
            "timers": self._extract_freertos_timers(project_path),
            "config": self._parse_freertos_config(project_path),
            "memory": self._analyze_freertos_memory(project_path)
        }
        return analysis

    def _extract_freertos_tasks(self, project_path: Path) -> List[Dict[str, Any]]:
        """提取FreeRTOS任务信息"""
        tasks = []

        # 匹配xTaskCreate调用的正则表达式
        task_pattern = r'xTaskCreate\s*\(\s*(\w+)\s*,\s*"([^"]+)"\s*,\s*(\d+)\s*,\s*([^,]+)\s*,\s*(\d+)\s*,\s*([^)]+)\s*\)'

        for c_file in project_path.glob("**/*.c"):
            try:
                with open(c_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                matches = re.findall(task_pattern, content)
                for match in matches:
                    task_info = {
                        "entry_function": match[0],
                        "name": match[1],
                        "stack_size_words": int(match[2]),
                        "parameters": match[3],
                        "priority": int(match[4]),
                        "handle": match[5],
                        "source_file": str(c_file.relative_to(project_path))
                    }
                    tasks.append(task_info)
            except Exception:
                continue

        return tasks

    def _parse_freertos_config(self, project_path: Path) -> Dict[str, Any]:
        """解析FreeRTOSConfig.h配置"""
        config = {}

        config_files = list(project_path.glob("**/FreeRTOSConfig.h"))
        if not config_files:
            return config

        config_file = config_files[0]

        try:
            with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # 提取关键配置项
            config_patterns = {
                "configUSE_PREEMPTION": r'#define\s+configUSE_PREEMPTION\s+(\d+)',
                "configUSE_IDLE_HOOK": r'#define\s+configUSE_IDLE_HOOK\s+(\d+)',
                "configUSE_TICK_HOOK": r'#define\s+configUSE_TICK_HOOK\s+(\d+)',
                "configCPU_CLOCK_HZ": r'#define\s+configCPU_CLOCK_HZ\s+\(\s*(\d+)\s*\)',
                "configTICK_RATE_HZ": r'#define\s+configTICK_RATE_HZ\s+\(\s*(\d+)\s*\)',
                "configMAX_PRIORITIES": r'#define\s+configMAX_PRIORITIES\s+\(\s*(\d+)\s*\)',
                "configMINIMAL_STACK_SIZE": r'#define\s+configMINIMAL_STACK_SIZE\s+\(\s*(\d+)\s*\)',
                "configTOTAL_HEAP_SIZE": r'#define\s+configTOTAL_HEAP_SIZE\s+\(\s*(\d+)\s*\)'
            }

            for config_name, pattern in config_patterns.items():
                match = re.search(pattern, content)
                if match:
                    config[config_name] = int(match.group(1))

        except Exception:
            pass

        return config

    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "project_path": {"type": "string"},
                "analysis_config": {
                    "type": "object",
                    "properties": {
                        "deep_analysis": {"type": "boolean"},
                        "extract_task_details": {"type": "boolean"},
                        "analyze_memory_usage": {"type": "boolean"}
                    }
                }
            },
            "required": ["project_path"]
        }
```

这个实现文档提供了项目的核心技术实现细节，包括MCP服务的具体实现、核心组件的代码结构、部署配置和测试策略。通过这些实现，可以构建一个完整的STM32到NXP MCU代码迁移工具。
