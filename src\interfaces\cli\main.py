#!/usr/bin/env python3
"""
STM32到NXP MCU代码迁移工具 - 命令行界面
"""

import argparse
import json
import sys
import logging
from pathlib import Path
from typing import Dict, Any

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from mcp_services.base_mcp import service_registry
from mcp_services.analysis.analyze_pin_configuration_mcp import AnalyzePinConfigurationMCP
from mcp_services.codegen.nxp_uart_codegen_mcp import NXPUARTCodegenMCP

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MigrationCLI:
    """迁移工具命令行界面"""
    
    def __init__(self):
        self.parser = self._create_parser()
        self._register_services()
    
    def _create_parser(self) -> argparse.ArgumentParser:
        """创建命令行参数解析器"""
        parser = argparse.ArgumentParser(
            description="STM32到NXP MCU代码迁移工具",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
示例用法:
  # 分析STM32项目的引脚配置
  python main.py analyze-pins /path/to/stm32_project
  
  # 生成NXP UART代码
  python main.py generate-uart --config uart_config.json --target MCXN947
  
  # 列出所有可用的MCP服务
  python main.py list-services
            """
        )
        
        subparsers = parser.add_subparsers(dest='command', help='可用命令')
        
        # 分析引脚配置命令
        analyze_pins_parser = subparsers.add_parser(
            'analyze-pins', 
            help='分析STM32项目的引脚配置'
        )
        analyze_pins_parser.add_argument(
            'project_path', 
            help='STM32项目路径'
        )
        analyze_pins_parser.add_argument(
            '--output', '-o',
            help='输出文件路径（JSON格式）'
        )
        analyze_pins_parser.add_argument(
            '--deep-analysis',
            action='store_true',
            help='启用深度分析'
        )
        
        # 生成UART代码命令
        generate_uart_parser = subparsers.add_parser(
            'generate-uart',
            help='生成NXP UART代码'
        )
        generate_uart_parser.add_argument(
            '--config',
            required=True,
            help='UART配置文件路径（JSON格式）'
        )
        generate_uart_parser.add_argument(
            '--target',
            default='MCXN947',
            help='目标NXP MCU型号'
        )
        generate_uart_parser.add_argument(
            '--output-dir',
            default='./generated',
            help='代码输出目录'
        )
        
        # 列出服务命令
        list_services_parser = subparsers.add_parser(
            'list-services',
            help='列出所有可用的MCP服务'
        )
        
        # 测试服务命令
        test_service_parser = subparsers.add_parser(
            'test-service',
            help='测试指定的MCP服务'
        )
        test_service_parser.add_argument(
            'service_name',
            help='MCP服务名称'
        )
        test_service_parser.add_argument(
            '--input',
            required=True,
            help='输入数据文件路径（JSON格式）'
        )
        
        return parser
    
    def _register_services(self):
        """注册MCP服务"""
        # 注册分析服务
        pin_analysis_service = AnalyzePinConfigurationMCP()
        service_registry.register(pin_analysis_service)
        
        # 注册代码生成服务
        uart_codegen_service = NXPUARTCodegenMCP()
        service_registry.register(uart_codegen_service)
        
        logger.info("已注册所有MCP服务")
    
    def run(self):
        """运行CLI"""
        args = self.parser.parse_args()
        
        if not args.command:
            self.parser.print_help()
            return
        
        try:
            if args.command == 'analyze-pins':
                self._handle_analyze_pins(args)
            elif args.command == 'generate-uart':
                self._handle_generate_uart(args)
            elif args.command == 'list-services':
                self._handle_list_services(args)
            elif args.command == 'test-service':
                self._handle_test_service(args)
            else:
                print(f"未知命令: {args.command}")
                self.parser.print_help()
                
        except Exception as e:
            logger.error(f"执行命令失败: {e}")
            sys.exit(1)
    
    def _handle_analyze_pins(self, args):
        """处理引脚分析命令"""
        project_path = Path(args.project_path)
        if not project_path.exists():
            raise ValueError(f"项目路径不存在: {project_path}")
        
        # 准备输入数据
        input_data = {
            "project_path": str(project_path),
            "analysis_config": {
                "deep_analysis": args.deep_analysis,
                "include_generated_code": False,
                "generate_mapping_suggestions": True
            }
        }
        
        # 执行分析
        service = service_registry.get_service("analyze_pin_configuration_mcp")
        if not service:
            raise ValueError("引脚配置分析服务未注册")
        
        logger.info(f"开始分析项目: {project_path}")
        result = service.execute(input_data)
        
        # 输出结果
        if args.output:
            output_path = Path(args.output)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            logger.info(f"分析结果已保存到: {output_path}")
        else:
            print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 显示摘要信息
        if result["status"] == "success":
            data = result["data"]
            print(f"\n分析摘要:")
            print(f"  总引脚数: {data['total_pins']}")
            print(f"  MCU封装: {data['mcu_package']}")
            print(f"  GPIO引脚: {data['pin_analysis']['gpio_pins']}")
            print(f"  复用功能引脚: {data['pin_analysis']['alternate_function_pins']}")
            print(f"  外设使用情况: {len(data['pin_analysis']['peripheral_usage'])} 个外设")
    
    def _handle_generate_uart(self, args):
        """处理UART代码生成命令"""
        config_path = Path(args.config)
        if not config_path.exists():
            raise ValueError(f"配置文件不存在: {config_path}")
        
        # 读取配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            uart_config = json.load(f)
        
        # 准备输入数据
        input_data = {
            "uart_configurations": uart_config.get("uart_configurations", []),
            "target_mcu": args.target,
            "pin_mappings": uart_config.get("pin_mappings", {})
        }
        
        # 执行代码生成
        service = service_registry.get_service("nxp_uart_codegen_mcp")
        if not service:
            raise ValueError("NXP UART代码生成服务未注册")
        
        logger.info(f"开始生成UART代码，目标MCU: {args.target}")
        result = service.execute(input_data)
        
        if result["status"] == "success":
            # 创建输出目录
            output_dir = Path(args.output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存生成的文件
            generated_files = result["data"]["generated_files"]
            for filename, code in generated_files.items():
                file_path = output_dir / filename
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(code)
                logger.info(f"已生成文件: {file_path}")
            
            # 保存结果摘要
            summary_path = output_dir / "generation_summary.json"
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            
            # 显示摘要信息
            data = result["data"]
            print(f"\n代码生成摘要:")
            print(f"  生成文件数: {data['code_quality_metrics']['total_files']}")
            print(f"  总代码行数: {data['code_quality_metrics']['total_lines']}")
            print(f"  注释率: {data['code_quality_metrics']['comment_ratio']:.2%}")
            print(f"  UART实例数: {data['uart_count']}")
            print(f"  目标UART类型: {data['target_uart_type']}")
        else:
            logger.error(f"代码生成失败: {result.get('error', {}).get('message', '未知错误')}")
    
    def _handle_list_services(self, args):
        """处理列出服务命令"""
        services = service_registry.list_services()
        
        print("已注册的MCP服务:")
        print("=" * 50)
        
        for service_name, service_info in services.items():
            print(f"\n服务名称: {service_name}")
            print(f"版本: {service_info['version']}")
            print(f"描述: {service_info['description']}")
            
            # 显示输入Schema的主要字段
            input_props = service_info['input_schema'].get('properties', {})
            if input_props:
                print("输入参数:")
                for prop_name, prop_info in input_props.items():
                    prop_type = prop_info.get('type', 'unknown')
                    print(f"  - {prop_name} ({prop_type})")
    
    def _handle_test_service(self, args):
        """处理测试服务命令"""
        service = service_registry.get_service(args.service_name)
        if not service:
            raise ValueError(f"服务未找到: {args.service_name}")
        
        # 读取输入数据
        input_path = Path(args.input)
        if not input_path.exists():
            raise ValueError(f"输入文件不存在: {input_path}")
        
        with open(input_path, 'r', encoding='utf-8') as f:
            input_data = json.load(f)
        
        # 执行服务
        logger.info(f"测试服务: {args.service_name}")
        result = service.execute(input_data)
        
        # 输出结果
        print(json.dumps(result, indent=2, ensure_ascii=False))


def main():
    """主函数"""
    cli = MigrationCLI()
    cli.run()


if __name__ == "__main__":
    main()
