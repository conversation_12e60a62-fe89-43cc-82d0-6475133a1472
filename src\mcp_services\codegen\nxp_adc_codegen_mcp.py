
# src/mcp_services/codegen/nxp_adc_codegen_mcp.py
from jinja2 import Template
from typing import Dict, Any

from ..base_mcp import BaseMCP

class NXPADCCodegenMCP(BaseMCP):
    """NXP ADC代码生成MCP服务"""

    def __init__(self):
        super().__init__("nxp_adc_codegen_mcp")
        self.adc_mapping = {
            "ADC1": "ADC1",
            "ADC2": "ADC2",
        }

    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        stm32_adc_configs = input_data["adc_configurations"]
        
        generated_files = {}
        for adc_config in stm32_adc_configs:
            code_result = self._generate_adc_code(adc_config)
            instance_name = self.adc_mapping.get(adc_config["instance"], "ADC1")
            generated_files[f"{instance_name.lower()}_config.c"] = code_result["c_code"]
        
        return {
            "generated_files": generated_files,
            "header_includes": ["fsl_adc.h", "fsl_clock.h"]
        }

    def _generate_adc_code(self, stm32_config: Dict) -> Dict[str, Any]:
        nxp_instance = self.adc_mapping.get(stm32_config["instance"], "ADC1")
        
        template = Template("""
/* {{nxp_instance}} initialization */
void {{nxp_instance}}_Init(void)
{
    adc_config_t {{nxp_instance.lower()}}_config;

    ADC_GetDefaultConfig(&{{nxp_instance.lower()}}_config);
    {{nxp_instance.lower()}}_config.resolution = {{resolution}};
    {{nxp_instance.lower()}}_config.enableContinuousConversion = {{continuous_conv_mode}};

    ADC_Init({{nxp_instance}}, &{{nxp_instance.lower()}}_config);
}
        """)
        
        code = template.render(
            nxp_instance=nxp_instance,
            resolution=self._map_resolution(stm32_config["resolution"]),
            continuous_conv_mode="true" if stm32_config["continuous_conv_mode"] else "false"
        )
        
        return {
            "c_code": code.strip()
        }

    def _map_resolution(self, resolution: str) -> str:
        res_map = {
            "12": "kADC_Resolution12Bit",
            "10": "kADC_Resolution10Bit",
            "8": "kADC_Resolution8Bit"
        }
        return res_map.get(resolution, "kADC_Resolution12Bit")

    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "adc_configurations": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "instance": {"type": "string"},
                            "resolution": {"type": "string"},
                            "continuous_conv_mode": {"type": "boolean"}
                        }
                    }
                }
            }
        }

    def get_output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "generated_files": {
                    "type": "object"
                },
                "header_includes": {
                    "type": "array"
                }
            }
        }
