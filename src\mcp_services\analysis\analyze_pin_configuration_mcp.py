"""
引脚配置分析MCP服务
解析STM32CubeMX生成的.ioc文件中的引脚配置信息
"""

import configparser
import re
from pathlib import Path
from typing import Dict, Any, List, Optional
import logging

from ..base_mcp import AnalysisMCP

logger = logging.getLogger(__name__)

class AnalyzePinConfigurationMCP(AnalysisMCP):
    """引脚配置分析MCP服务"""
    
    def __init__(self):
        super().__init__("analyze_pin_configuration_mcp")
        
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析STM32项目的引脚配置"""
        project_path = Path(input_data["project_path"])
        analysis_config = input_data.get("analysis_config", {})
        
        # 查找.ioc文件
        ioc_files = list(project_path.glob("**/*.ioc"))
        if not ioc_files:
            raise ValueError("未找到STM32CubeMX配置文件(.ioc)")
        
        ioc_file = ioc_files[0]  # 使用第一个找到的.ioc文件
        logger.info(f"分析引脚配置文件: {ioc_file}")
        
        # 解析引脚配置
        pin_configs = self._parse_ioc_file(ioc_file)
        
        # 分析引脚使用情况
        pin_analysis = self._analyze_pin_usage(pin_configs)
        
        # 生成引脚映射建议
        mapping_suggestions = self._generate_mapping_suggestions(pin_configs)
        
        return {
            "pin_configurations": pin_configs,
            "pin_analysis": pin_analysis,
            "mapping_suggestions": mapping_suggestions,
            "total_pins": len(pin_configs),
            "source_file": str(ioc_file.relative_to(project_path)),
            "mcu_package": self._extract_mcu_package(ioc_file)
        }
    
    def _parse_ioc_file(self, ioc_file: Path) -> List[Dict[str, Any]]:
        """解析.ioc文件中的引脚配置"""
        config = configparser.ConfigParser()
        config.read(ioc_file, encoding='utf-8')
        
        pins = []
        pin_data = {}
        
        # 遍历所有配置项，收集引脚相关信息
        for section_name in config.sections():
            for key, value in config[section_name].items():
                if self._is_pin_config(key):
                    pin_name, attribute = self._parse_pin_key(key)
                    if pin_name not in pin_data:
                        pin_data[pin_name] = {"pin_name": pin_name}
                    
                    pin_data[pin_name][attribute] = value
        
        # 转换为列表格式并补充完整信息
        for pin_name, data in pin_data.items():
            pin_config = self._build_complete_pin_config(data)
            if pin_config:
                pins.append(pin_config)
        
        return sorted(pins, key=lambda x: x["pin_name"])
    
    def _is_pin_config(self, key: str) -> bool:
        """判断是否为引脚配置项"""
        pin_pattern = r'^P[A-Z]\d+\.'
        return bool(re.match(pin_pattern, key))
    
    def _parse_pin_key(self, key: str) -> tuple:
        """解析引脚配置键，返回引脚名称和属性"""
        parts = key.split('.')
        pin_name = parts[0]  # 如 PA9
        attribute = parts[1] if len(parts) > 1 else "unknown"
        return pin_name, attribute
    
    def _build_complete_pin_config(self, pin_data: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """构建完整的引脚配置信息"""
        pin_name = pin_data.get("pin_name")
        if not pin_name:
            return None
        
        # 提取引脚的端口和编号
        port_match = re.match(r'P([A-Z])(\d+)', pin_name)
        if not port_match:
            return None
        
        port = port_match.group(1)
        pin_number = int(port_match.group(2))
        
        config = {
            "pin_name": pin_name,
            "port": port,
            "pin_number": pin_number,
            "signal": pin_data.get("Signal", "GPIO"),
            "mode": pin_data.get("Mode", "Input"),
            "pull": pin_data.get("Pull", "NoPull"),
            "speed": pin_data.get("Speed", "Low"),
            "locked": pin_data.get("Locked", "false").lower() == "true",
            "user_label": pin_data.get("UserLabel", ""),
            "gpio_config": self._extract_gpio_config(pin_data),
            "alternate_function": self._extract_alternate_function(pin_data.get("Signal", ""))
        }
        
        return config
    
    def _extract_gpio_config(self, pin_data: Dict[str, str]) -> Dict[str, Any]:
        """提取GPIO配置信息"""
        config = {}
        
        # GPIO输出状态
        if "GPIOParameters" in pin_data:
            gpio_params = pin_data["GPIOParameters"]
            if "GPIO_PinState" in gpio_params:
                config["initial_state"] = pin_data.get("GPIO_PinState", "GPIO_PIN_RESET")
        
        # GPIO模式映射
        mode_mapping = {
            "GPIO_MODE_INPUT": "Input",
            "GPIO_MODE_OUTPUT_PP": "Output_PP",
            "GPIO_MODE_OUTPUT_OD": "Output_OD",
            "GPIO_MODE_AF_PP": "Alternate_PP",
            "GPIO_MODE_AF_OD": "Alternate_OD",
            "GPIO_MODE_ANALOG": "Analog",
            "GPIO_MODE_IT_RISING": "IT_Rising",
            "GPIO_MODE_IT_FALLING": "IT_Falling",
            "GPIO_MODE_IT_RISING_FALLING": "IT_Rising_Falling"
        }
        
        mode = pin_data.get("Mode", "")
        config["mode_detail"] = mode_mapping.get(mode, mode)
        
        return config
    
    def _extract_alternate_function(self, signal: str) -> Dict[str, Any]:
        """提取复用功能信息"""
        if not signal or signal == "GPIO":
            return {"type": "GPIO", "peripheral": None, "function": None}
        
        # 解析信号格式，如 "USART1_TX", "I2C1_SCL", "TIM2_CH1"
        parts = signal.split('_')
        if len(parts) >= 2:
            peripheral = parts[0]
            function = '_'.join(parts[1:])
            
            # 确定外设类型
            peripheral_types = {
                "USART": "UART", "UART": "UART", "LPUART": "UART",
                "I2C": "I2C", "LPI2C": "I2C",
                "SPI": "SPI", "LPSPI": "SPI",
                "TIM": "Timer", "LPTIM": "Timer",
                "ADC": "ADC",
                "DAC": "DAC",
                "CAN": "CAN",
                "USB": "USB",
                "ETH": "Ethernet"
            }
            
            peripheral_type = None
            for key, value in peripheral_types.items():
                if peripheral.startswith(key):
                    peripheral_type = value
                    break
            
            return {
                "type": "AlternateFunction",
                "peripheral": peripheral,
                "function": function,
                "peripheral_type": peripheral_type or "Unknown"
            }
        
        return {"type": "Unknown", "peripheral": signal, "function": None}
    
    def _analyze_pin_usage(self, pin_configs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析引脚使用情况"""
        analysis = {
            "total_pins": len(pin_configs),
            "gpio_pins": 0,
            "alternate_function_pins": 0,
            "analog_pins": 0,
            "peripheral_usage": {},
            "port_usage": {}
        }
        
        for pin_config in pin_configs:
            # 统计引脚类型
            af_info = pin_config["alternate_function"]
            if af_info["type"] == "GPIO":
                analysis["gpio_pins"] += 1
            elif af_info["type"] == "AlternateFunction":
                analysis["alternate_function_pins"] += 1
            
            if "Analog" in pin_config["mode"]:
                analysis["analog_pins"] += 1
            
            # 统计外设使用情况
            if af_info["peripheral"]:
                peripheral = af_info["peripheral"]
                if peripheral not in analysis["peripheral_usage"]:
                    analysis["peripheral_usage"][peripheral] = {
                        "count": 0,
                        "pins": [],
                        "type": af_info.get("peripheral_type", "Unknown")
                    }
                analysis["peripheral_usage"][peripheral]["count"] += 1
                analysis["peripheral_usage"][peripheral]["pins"].append(pin_config["pin_name"])
            
            # 统计端口使用情况
            port = pin_config["port"]
            if port not in analysis["port_usage"]:
                analysis["port_usage"][port] = {"count": 0, "pins": []}
            analysis["port_usage"][port]["count"] += 1
            analysis["port_usage"][port]["pins"].append(pin_config["pin_name"])
        
        return analysis
    
    def _generate_mapping_suggestions(self, pin_configs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成NXP引脚映射建议"""
        suggestions = {
            "critical_pins": [],
            "flexible_pins": [],
            "potential_conflicts": [],
            "mapping_notes": []
        }
        
        for pin_config in pin_configs:
            af_info = pin_config["alternate_function"]
            
            # 关键引脚（特定外设功能）
            if af_info["type"] == "AlternateFunction":
                if af_info["peripheral_type"] in ["UART", "I2C", "SPI"]:
                    suggestions["critical_pins"].append({
                        "stm32_pin": pin_config["pin_name"],
                        "function": pin_config["signal"],
                        "peripheral_type": af_info["peripheral_type"],
                        "priority": "high"
                    })
            
            # 灵活引脚（GPIO）
            elif af_info["type"] == "GPIO":
                suggestions["flexible_pins"].append({
                    "stm32_pin": pin_config["pin_name"],
                    "mode": pin_config["mode"],
                    "priority": "low"
                })
        
        # 添加映射注意事项
        suggestions["mapping_notes"] = [
            "UART引脚需要确保NXP MCU有对应的UART外设",
            "I2C引脚需要支持开漏输出模式",
            "SPI引脚需要确认主从模式配置",
            "GPIO引脚可以灵活映射到任意可用引脚"
        ]
        
        return suggestions
    
    def _extract_mcu_package(self, ioc_file: Path) -> str:
        """提取MCU封装信息"""
        try:
            config = configparser.ConfigParser()
            config.read(ioc_file, encoding='utf-8')
            
            # 查找MCU相关配置
            for section_name in config.sections():
                for key, value in config[section_name].items():
                    if "Mcu.Package" in key:
                        return value
                    elif "Mcu.Name" in key and "LQFP" in value:
                        # 从MCU名称中提取封装信息
                        package_match = re.search(r'(LQFP\d+|BGA\d+|TQFP\d+)', value)
                        if package_match:
                            return package_match.group(1)
        except Exception as e:
            logger.warning(f"无法提取MCU封装信息: {e}")
        
        return "Unknown"
    
    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "project_path": {"type": "string"},
                "analysis_config": {
                    "type": "object",
                    "properties": {
                        "deep_analysis": {"type": "boolean"},
                        "include_generated_code": {"type": "boolean"},
                        "generate_mapping_suggestions": {"type": "boolean"}
                    }
                }
            },
            "required": ["project_path"]
        }
    
    def get_output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "pin_configurations": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "pin_name": {"type": "string"},
                            "port": {"type": "string"},
                            "pin_number": {"type": "integer"},
                            "signal": {"type": "string"},
                            "mode": {"type": "string"},
                            "pull": {"type": "string"},
                            "speed": {"type": "string"},
                            "locked": {"type": "boolean"},
                            "user_label": {"type": "string"},
                            "gpio_config": {"type": "object"},
                            "alternate_function": {"type": "object"}
                        }
                    }
                },
                "pin_analysis": {"type": "object"},
                "mapping_suggestions": {"type": "object"},
                "total_pins": {"type": "integer"},
                "source_file": {"type": "string"},
                "mcu_package": {"type": "string"}
            }
        }
