#!/usr/bin/env python3
"""
MCP服务器实现，用于与VSCode等MCP客户端集成
"""

import asyncio
import json
import sys
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from mcp_services.base_mcp import service_registry
from mcp_services.analysis.analyze_pin_configuration_mcp import AnalyzePinConfigurationMCP
from mcp_services.codegen.nxp_uart_codegen_mcp import NXPUARTCodegenMCP

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mcp_server.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MCPServer:
    """MCP服务器实现"""
    
    def __init__(self):
        self.services = {}
        self.tools = {}
        self._register_services()
        self._register_tools()
    
    def _register_services(self):
        """注册MCP服务"""
        # 注册分析服务
        pin_analysis_service = AnalyzePinConfigurationMCP()
        service_registry.register(pin_analysis_service)
        
        # 注册代码生成服务
        uart_codegen_service = NXPUARTCodegenMCP()
        service_registry.register(uart_codegen_service)
        
        logger.info("已注册所有MCP服务")
    
    def _register_tools(self):
        """注册MCP工具"""
        self.tools = {
            "analyze_stm32_pins": {
                "description": "分析STM32项目的引脚配置",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "project_path": {
                            "type": "string",
                            "description": "STM32项目路径"
                        },
                        "deep_analysis": {
                            "type": "boolean",
                            "description": "是否启用深度分析",
                            "default": True
                        }
                    },
                    "required": ["project_path"]
                }
            },
            "generate_nxp_uart": {
                "description": "生成NXP UART初始化代码",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "uart_configurations": {
                            "type": "array",
                            "description": "UART配置列表",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "instance": {"type": "string"},
                                    "baudrate": {"type": "integer"},
                                    "data_bits": {"type": "integer"},
                                    "stop_bits": {"type": "integer"},
                                    "parity": {"type": "string"}
                                }
                            }
                        },
                        "target_mcu": {
                            "type": "string",
                            "description": "目标NXP MCU型号",
                            "default": "MCXN947"
                        }
                    },
                    "required": ["uart_configurations"]
                }
            },
            "list_mcp_services": {
                "description": "列出所有可用的MCP服务",
                "inputSchema": {
                    "type": "object",
                    "properties": {}
                }
            }
        }
    
    async def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理MCP请求"""
        try:
            method = request.get("method")
            params = request.get("params", {})
            request_id = request.get("id")
            
            if method == "initialize":
                return await self._handle_initialize(request_id, params)
            elif method == "tools/list":
                return await self._handle_list_tools(request_id)
            elif method == "tools/call":
                return await self._handle_call_tool(request_id, params)
            else:
                return self._error_response(request_id, -32601, f"Method not found: {method}")
                
        except Exception as e:
            logger.error(f"处理请求失败: {e}")
            return self._error_response(request.get("id"), -32603, str(e))
    
    async def _handle_initialize(self, request_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理初始化请求"""
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "result": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {}
                },
                "serverInfo": {
                    "name": "STM32-to-NXP Migration Tool",
                    "version": "1.0.0"
                }
            }
        }
    
    async def _handle_list_tools(self, request_id: str) -> Dict[str, Any]:
        """处理列出工具请求"""
        tools_list = []
        for tool_name, tool_info in self.tools.items():
            tools_list.append({
                "name": tool_name,
                "description": tool_info["description"],
                "inputSchema": tool_info["inputSchema"]
            })
        
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "result": {
                "tools": tools_list
            }
        }
    
    async def _handle_call_tool(self, request_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理工具调用请求"""
        tool_name = params.get("name")
        arguments = params.get("arguments", {})
        
        if tool_name == "analyze_stm32_pins":
            return await self._call_analyze_pins(request_id, arguments)
        elif tool_name == "generate_nxp_uart":
            return await self._call_generate_uart(request_id, arguments)
        elif tool_name == "list_mcp_services":
            return await self._call_list_services(request_id, arguments)
        else:
            return self._error_response(request_id, -32602, f"Unknown tool: {tool_name}")
    
    async def _call_analyze_pins(self, request_id: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用引脚分析工具"""
        try:
            service = service_registry.get_service("analyze_pin_configuration_mcp")
            if not service:
                return self._error_response(request_id, -32603, "引脚分析服务未找到")
            
            input_data = {
                "project_path": arguments["project_path"],
                "analysis_config": {
                    "deep_analysis": arguments.get("deep_analysis", True),
                    "include_generated_code": False,
                    "generate_mapping_suggestions": True
                }
            }
            
            result = service.execute(input_data)
            
            if result["status"] == "success":
                # 格式化输出为用户友好的文本
                data = result["data"]
                content = self._format_pin_analysis_result(data)
                
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {
                        "content": [
                            {
                                "type": "text",
                                "text": content
                            }
                        ]
                    }
                }
            else:
                error_msg = result.get("error", {}).get("message", "分析失败")
                return self._error_response(request_id, -32603, error_msg)
                
        except Exception as e:
            logger.error(f"引脚分析失败: {e}")
            return self._error_response(request_id, -32603, str(e))
    
    async def _call_generate_uart(self, request_id: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用UART代码生成工具"""
        try:
            service = service_registry.get_service("nxp_uart_codegen_mcp")
            if not service:
                return self._error_response(request_id, -32603, "UART代码生成服务未找到")
            
            result = service.execute(arguments)
            
            if result["status"] == "success":
                # 格式化输出为代码块
                data = result["data"]
                content = self._format_uart_generation_result(data)
                
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {
                        "content": [
                            {
                                "type": "text",
                                "text": content
                            }
                        ]
                    }
                }
            else:
                error_msg = result.get("error", {}).get("message", "代码生成失败")
                return self._error_response(request_id, -32603, error_msg)
                
        except Exception as e:
            logger.error(f"UART代码生成失败: {e}")
            return self._error_response(request_id, -32603, str(e))
    
    async def _call_list_services(self, request_id: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """列出所有MCP服务"""
        try:
            services = service_registry.list_services()
            content = self._format_services_list(services)
            
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "content": [
                        {
                            "type": "text",
                            "text": content
                        }
                    ]
                }
            }
        except Exception as e:
            logger.error(f"列出服务失败: {e}")
            return self._error_response(request_id, -32603, str(e))
    
    def _format_pin_analysis_result(self, data: Dict[str, Any]) -> str:
        """格式化引脚分析结果"""
        content = "# STM32引脚配置分析结果\n\n"
        
        # 基本信息
        content += f"**项目信息:**\n"
        content += f"- 总引脚数: {data['total_pins']}\n"
        content += f"- MCU封装: {data['mcu_package']}\n"
        content += f"- 配置文件: {data['source_file']}\n\n"
        
        # 引脚使用统计
        pin_analysis = data['pin_analysis']
        content += f"**引脚使用统计:**\n"
        content += f"- GPIO引脚: {pin_analysis['gpio_pins']}\n"
        content += f"- 复用功能引脚: {pin_analysis['alternate_function_pins']}\n"
        content += f"- 模拟引脚: {pin_analysis['analog_pins']}\n\n"
        
        # 外设使用情况
        if pin_analysis['peripheral_usage']:
            content += f"**外设使用情况:**\n"
            for peripheral, info in pin_analysis['peripheral_usage'].items():
                content += f"- {peripheral} ({info['type']}): {info['count']} 个引脚\n"
                content += f"  引脚: {', '.join(info['pins'])}\n"
            content += "\n"
        
        # 映射建议
        mapping_suggestions = data['mapping_suggestions']
        if mapping_suggestions['critical_pins']:
            content += f"**关键引脚映射建议:**\n"
            for pin in mapping_suggestions['critical_pins']:
                content += f"- {pin['stm32_pin']}: {pin['function']} ({pin['peripheral_type']})\n"
            content += "\n"
        
        # 映射注意事项
        if mapping_suggestions['mapping_notes']:
            content += f"**映射注意事项:**\n"
            for note in mapping_suggestions['mapping_notes']:
                content += f"- {note}\n"
        
        return content
    
    def _format_uart_generation_result(self, data: Dict[str, Any]) -> str:
        """格式化UART代码生成结果"""
        content = "# NXP UART代码生成结果\n\n"
        
        # 基本信息
        content += f"**生成信息:**\n"
        content += f"- UART实例数: {data['uart_count']}\n"
        content += f"- 目标UART类型: {data['target_uart_type']}\n"
        content += f"- 生成文件数: {data['code_quality_metrics']['total_files']}\n"
        content += f"- 总代码行数: {data['code_quality_metrics']['total_lines']}\n"
        content += f"- 注释率: {data['code_quality_metrics']['comment_ratio']:.2%}\n\n"
        
        # 生成的文件
        content += f"**生成的文件:**\n"
        for filename, code in data['generated_files'].items():
            content += f"\n### {filename}\n\n"
            content += f"```c\n{code}\n```\n"
        
        # 头文件依赖
        content += f"\n**需要的头文件:**\n"
        for header in data['header_includes']:
            content += f"- `{header}`\n"
        
        return content
    
    def _format_services_list(self, services: Dict[str, Dict[str, Any]]) -> str:
        """格式化服务列表"""
        content = "# 可用的MCP服务\n\n"
        
        for service_name, service_info in services.items():
            content += f"## {service_name}\n\n"
            content += f"**版本:** {service_info['version']}\n\n"
            content += f"**描述:** {service_info['description']}\n\n"
            
            # 输入参数
            input_props = service_info['input_schema'].get('properties', {})
            if input_props:
                content += f"**输入参数:**\n"
                for prop_name, prop_info in input_props.items():
                    prop_type = prop_info.get('type', 'unknown')
                    description = prop_info.get('description', '')
                    content += f"- `{prop_name}` ({prop_type}): {description}\n"
                content += "\n"
        
        return content
    
    def _error_response(self, request_id: str, code: int, message: str) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "error": {
                "code": code,
                "message": message
            }
        }
    
    async def run(self):
        """运行MCP服务器"""
        logger.info("启动MCP服务器...")
        
        try:
            while True:
                # 从stdin读取请求
                line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
                if not line:
                    break
                
                try:
                    request = json.loads(line.strip())
                    response = await self.handle_request(request)
                    
                    # 输出响应到stdout
                    print(json.dumps(response))
                    sys.stdout.flush()
                    
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析错误: {e}")
                    error_response = self._error_response(None, -32700, "Parse error")
                    print(json.dumps(error_response))
                    sys.stdout.flush()
                    
        except KeyboardInterrupt:
            logger.info("收到中断信号，关闭服务器")
        except Exception as e:
            logger.error(f"服务器运行错误: {e}")
        finally:
            logger.info("MCP服务器已关闭")


async def main():
    """主函数"""
    server = MCPServer()
    await server.run()


if __name__ == "__main__":
    asyncio.run(main())
