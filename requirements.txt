# STM32到NXP MCU代码迁移工具依赖包

# 核心依赖
jsonschema>=4.0.0
jinja2>=3.0.0
pathlib2>=2.3.0

# 代码分析
tree-sitter>=0.20.0
tree-sitter-c>=0.20.0

# Web框架（可选）
fastapi>=0.68.0
uvicorn>=0.15.0
pydantic>=1.8.0

# 数据库（可选）
sqlalchemy>=1.4.0
alembic>=1.7.0

# 测试框架
pytest>=6.0.0
pytest-asyncio>=0.15.0
pytest-cov>=2.12.0

# 代码质量
black>=21.0.0
flake8>=3.9.0
mypy>=0.910

# 日志和监控
structlog>=21.0.0
psutil>=5.8.0

# 开发工具
pre-commit>=2.15.0
isort>=5.9.0

# 文档生成
sphinx>=4.0.0
sphinx-rtd-theme>=0.5.0

# 配置管理
pyyaml>=5.4.0
python-dotenv>=0.19.0

# HTTP客户端（用于LLM API调用）
httpx>=0.24.0
aiohttp>=3.8.0

# 加密和安全
cryptography>=3.4.0

# 并发处理
asyncio-throttle>=1.0.0

# 命令行界面增强
click>=8.0.0
rich>=10.0.0
typer>=0.4.0

# 数据处理
pandas>=1.3.0
numpy>=1.21.0

# 版本控制
gitpython>=3.1.0
