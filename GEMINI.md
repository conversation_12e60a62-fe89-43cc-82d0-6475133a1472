# 项目核心战略：基于深度分析与原生仿写的代码生成

## 1. 核心任务

本项目的核心目标是，通过对优秀的 STM32 演示项目进行**极致细粒度的多维度分析**，并结合对 NXP MCU 的 **API 抽象与代码生成服务**，驱动大型语言模型（LLM）"仿写"和创作出功能对等、代码原生、高质量、可测试、可维护的 NXP MCU 演示项目。

## 2. 战略原则

*   **单一职责原则 (SRP)**：每个 MCP 服务只做一件具体、明确、定义良好的事。
*   **分析驱动生成**：代码生成不是基于简单的文本替换，而是基于对原始项目意图的深度理解。
*   **原生代码质量**：最终产出必须是符合目标平台（NXP）最佳实践的原生代码，而非"翻译腔"代码。
*   **质量是生命线**：整个流程必须建立严格的质量门禁（Quality Gates），确保每一步的输出都符合标准。

## 3. 核心工作流：三阶段法

```mermaid
graph TD
    A["阶段一: STM32 项目深度分析"] --> B{"主生成上下文"};
    C["阶段二: NXP 代码生成服务层"] --> B;
    B --> D["阶段三: LLM 驱动的代码生成流水线"];
```

---

### **阶段一：STM32 项目深度分析 (N-Dimension Extraction)**

此阶段的目标是将一个 STM32 项目彻底分解为一系列结构化的、可被机器理解的信息。我们将为此开发一套专门的 MCP 分析服务集群。

#### **类别 A：硬件与选型层分析 MCPs**

*   **`find_nxp_equivalent_mcp`**
    *   **职责**: 根据输入的 STM32 MCU 部件号，推荐对标的 NXP 部件号。
    *   **实现细节**: 基于芯片规格数据库进行特征匹配（内核架构、Flash/RAM大小、外设配置、封装类型等）
    *   **Data Schema (Output)**:
        ```jsonschema
        {
          "type": "object",
          "properties": {
            "source_stm32_part": { "type": "string" },
            "recommended_nxp_parts": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "part_number": { "type": "string" },
                  "match_score": { "type": "number", "minimum": 0, "maximum": 1 },
                  "core": { "type": "string" },
                  "flash_kb": { "type": "integer" },
                  "ram_kb": { "type": "integer" },
                  "package": { "type": "string" },
                  "peripheral_compatibility": {
                    "type": "object",
                    "properties": {
                      "uart_count": { "type": "integer" },
                      "spi_count": { "type": "integer" },
                      "i2c_count": { "type": "integer" },
                      "adc_channels": { "type": "integer" },
                      "timer_count": { "type": "integer" }
                    }
                  }
                }
              }
            }
          }
        }
        ```

*   **`analyze_pin_configuration_mcp`**
    *   **职责**: 解析项目的引脚配置文件，提取每个引脚的详细配置。
    *   **实现细节**: 解析STM32CubeMX生成的.ioc文件中的引脚配置部分
    *   **1.0版本实现示例**:
        ```python
        import configparser
        import re
        from typing import Dict, List, Any

        class IOCPinParser:
            def __init__(self, ioc_file_path: str):
                self.config = configparser.ConfigParser()
                self.config.read(ioc_file_path, encoding='utf-8')
                
            def extract_pin_configs(self) -> List[Dict[str, Any]]:
                """提取.ioc文件中的引脚配置"""
                pins = []
                
                # 解析引脚配置
                for section in self.config.sections():
                    for key, value in self.config[section].items():
                        if self._is_pin_config(key):
                            pin_info = self._parse_pin_line(key, value)
                            if pin_info:
                                pins.append(pin_info)
                                
                return pins
                
            def _is_pin_config(self, key: str) -> bool:
                """判断是否为引脚配置项"""
                pin_pattern = r'^P[A-Z]\d+\.'
                return bool(re.match(pin_pattern, key))
                
            def _parse_pin_line(self, key: str, value: str) -> Dict[str, Any]:
                """解析具体的引脚配置行"""
                # 解析引脚名称和属性
                parts = key.split('.')
                pin_name = parts[0]  # 如 PA9
                attribute = parts[1] if len(parts) > 1 else None
                
                return {
                    "pin_name": pin_name,
                    "attribute": attribute,
                    "value": value,
                    "parsed_config": self._interpret_pin_config(attribute, value)
                }
        ```
    *   **Data Schema (Output)**:
        ```jsonschema
        {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "pin_name": { "type": "string", "description": "例如: PA9" },
              "user_label": { "type": "string" },
              "signal": { "type": "string", "description": "例如: USART1_TX" },
              "mode": { "type": "string", "enum": ["Input", "Output_PP", "Alternate_PP", "Analog"] },
              "pull": { "type": "string", "enum": ["NoPull", "PullUp", "PullDown"] },
              "speed": { "type": "string", "enum": ["Low", "Medium", "Fast", "High"] },
              "locked": { "type": "boolean" },
              "gpio_config": {
                "type": "object",
                "properties": {
                  "initial_state": { "type": "string", "enum": ["Low", "High"] },
                  "max_output_speed": { "type": "string" }
                }
              }
            },
            "required": ["pin_name", "signal", "mode"]
          }
        }
        ```

*   **`analyze_clock_tree_mcp`**
    *   **职责**: 解析项目的时钟树配置。
    *   **实现细节**: 从.ioc文件中提取RCC相关配置，包括PLL设置、时钟分频等
    *   **1.0版本实现示例**:
        ```python
        class ClockTreeParser:
            def __init__(self, ioc_file_path: str):
                self.config = configparser.ConfigParser()
                self.config.read(ioc_file_path, encoding='utf-8')
                
            def extract_clock_config(self) -> Dict[str, Any]:
                """提取时钟树配置"""
                clock_config = {
                    "oscillators": self._parse_oscillators(),
                    "pll_config": self._parse_pll_config(),
                    "clock_dividers": self._parse_clock_dividers(),
                    "peripheral_clocks": self._parse_peripheral_clocks()
                }
                return clock_config
                
            def _parse_oscillators(self) -> Dict[str, Any]:
                """解析振荡器配置"""
                osc_config = {}
                
                # 查找HSE配置
                hse_state = self.config.get('RCC', 'RCC.HSEState', fallback='RCC_HSE_OFF')
                if 'ON' in hse_state or 'BYPASS' in hse_state:
                    hse_freq = self.config.get('RCC', 'RCC.HSE_VALUE', fallback='8000000')
                    osc_config['hse'] = {
                        'state': hse_state,
                        'frequency': int(hse_freq)
                    }
                
                # 查找LSE配置
                lse_state = self.config.get('RCC', 'RCC.LSEState', fallback='RCC_LSE_OFF')
                if 'ON' in lse_state:
                    osc_config['lse'] = {
                        'state': lse_state,
                        'frequency': 32768
                    }
                    
                return osc_config
        ```
    *   **Data Schema (Output)**:
        ```jsonschema
        {
          "type": "object",
          "properties": {
            "hse_hz": { "type": "integer" },
            "lse_hz": { "type": "integer" },
            "pll": {
              "type": "object",
              "properties": {
                "source": { "type": "string", "enum": ["HSI", "HSE"] },
                "mul": { "type": "integer" },
                "div": { "type": "integer" },
                "pllp": { "type": "integer" },
                "pllq": { "type": "integer" },
                "pllr": { "type": "integer" }
              }
            },
            "sysclk_hz": { "type": "integer" },
            "hclk_hz": { "type": "integer" },
            "pclk1_hz": { "type": "integer" },
            "pclk2_hz": { "type": "integer" },
            "calculated_frequencies": {
              "type": "object",
              "properties": {
                "usart1_clk": { "type": "integer" },
                "i2c1_clk": { "type": "integer" },
                "spi1_clk": { "type": "integer" },
                "adc_clk": { "type": "integer" }
              }
            }
          }
        }
        ```

#### **类别 B：项目与构建层分析 MCPs**

*   **`analyze_build_system_mcp`**
    *   **职责**: 识别并解析项目的构建系统。
    *   **实现细节**: 检测Makefile、CMakeLists.txt、.project文件等，提取编译选项和链接配置
    *   **1.0版本实现示例**:
        ```python
        class BuildSystemAnalyzer:
            def __init__(self, project_path: str):
                self.project_path = Path(project_path)
                
            def detect_build_system(self) -> Dict[str, Any]:
                """检测项目构建系统类型"""
                build_files = {
                    "Makefile": self.project_path / "Makefile",
                    "CMake": self.project_path / "CMakeLists.txt", 
                    "Keil": self.project_path.glob("*.uvprojx"),
                    "IAR": self.project_path.glob("*.ewp"),
                    "STM32CubeIDE": self.project_path / ".project"
                }
                
                detected_systems = []
                for build_type, file_path in build_files.items():
                    if build_type in ["Keil", "IAR"]:
                        if list(file_path):  # glob返回生成器
                            detected_systems.append(build_type)
                    elif file_path.exists():
                        detected_systems.append(build_type)
                        
                return {
                    "detected_systems": detected_systems,
                    "primary_system": detected_systems[0] if detected_systems else "Unknown",
                    "build_configs": self._extract_build_configs(detected_systems[0] if detected_systems else None)
                }
        ```
    *   **Data Schema (Output)**:
        ```jsonschema
        {
          "type": "object",
          "properties": {
            "type": { "type": "string", "enum": ["Makefile", "CMake", "Keil", "IAR", "STM32CubeIDE"] },
            "c_flags": { "type": "array", "items": { "type": "string" } },
            "asm_flags": { "type": "array", "items": { "type": "string" } },
            "ld_flags": { "type": "array", "items": { "type": "string" } },
            "include_paths": { "type": "array", "items": { "type": "string" } },
            "defines": { "type": "array", "items": { "type": "string" } },
            "linker_script": { "type": "string" },
            "optimization_level": { "type": "string", "enum": ["O0", "O1", "O2", "O3", "Os"] },
            "debug_info": { "type": "boolean" },
            "target_mcu": { "type": "string" }
          }
        }
        ```

*   **`analyze_project_structure_mcp`**
    *   **职责**: 分析项目的文件和目录结构。
    *   **实现细节**: 递归扫描项目目录，分类文件类型，识别代码组织模式
    *   **Data Schema (Output)**:
        ```jsonschema
        {
          "type": "object",
          "properties": {
            "root_directory": { "type": "string" },
            "source_files": {
              "type": "object",
              "properties": {
                "c_files": { "type": "array", "items": { "type": "string" } },
                "h_files": { "type": "array", "items": { "type": "string" } },
                "asm_files": { "type": "array", "items": { "type": "string" } }
              }
            },
            "directories": {
              "type": "object",
              "patternProperties": {
                ".*": { 
                  "type": "object",
                  "properties": {
                    "type": { "type": "string", "enum": ["source", "include", "driver", "middleware", "generated"] },
                    "files": { "type": "array", "items": { "type": "string" } },
                    "subdirectories": { "type": "array", "items": { "type": "string" } }
                  }
                }
              }
            },
            "project_metadata": {
              "type": "object",
              "properties": {
                "has_cubemx_generated": { "type": "boolean" },
                "has_hal_drivers": { "type": "boolean" },
                "has_freertos": { "type": "boolean" },
                "has_middleware": { "type": "boolean" }
              }
            }
          }
        }
        ```

*   **`analyze_rtos_usage_mcp`**
    *   **职责**: 检测项目是否使用了实时操作系统（RTOS）。
    *   **实现细节**: 扫描源代码中的RTOS API调用，分析任务创建和配置
    *   **1.0版本实现示例**:
        ```python
        class RTOSAnalyzer:
            def __init__(self, project_path: str):
                self.project_path = Path(project_path)
                self.rtos_signatures = {
                    "FreeRTOS": [
                        "xTaskCreate", "vTaskDelay", "xQueueCreate", 
                        "xSemaphoreCreate", "portTICK_PERIOD_MS"
                    ],
                    "ThreadX": [
                        "tx_thread_create", "tx_thread_sleep", 
                        "tx_queue_create", "tx_semaphore_create"
                    ]
                }
                
            def detect_rtos(self) -> Dict[str, Any]:
                """检测RTOS使用情况"""
                rtos_usage = {
                    "type": "None",
                    "confidence": 0.0,
                    "detected_apis": [],
                    "tasks": [],
                    "config_files": []
                }
                
                # 扫描所有C文件
                for c_file in self.project_path.glob("**/*.c"):
                    with open(c_file, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                    for rtos_type, signatures in self.rtos_signatures.items():
                        found_apis = [api for api in signatures if api in content]
                        if found_apis:
                            rtos_usage["type"] = rtos_type
                            rtos_usage["detected_apis"].extend(found_apis)
                            rtos_usage["confidence"] = len(found_apis) / len(signatures)
                            
                # 如果检测到FreeRTOS，进一步分析任务配置
                if rtos_usage["type"] == "FreeRTOS":
                    rtos_usage["tasks"] = self._extract_freertos_tasks()
                    
                return rtos_usage
        ```
    *   **Data Schema (Output)**:
        ```jsonschema
        {
          "type": "object",
          "properties": {
            "type": { "type": "string", "enum": ["FreeRTOS", "ThreadX", "None"] },
            "confidence": { "type": "number", "minimum": 0, "maximum": 1 },
            "detected_apis": { "type": "array", "items": { "type": "string" } },
            "tasks": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "name": { "type": "string" },
                  "priority": { "type": "integer" },
                  "stack_size_words": { "type": "integer" },
                  "entry_function": { "type": "string" },
                  "creation_location": { "type": "string" }
                }
              }
            },
            "config_files": { "type": "array", "items": { "type": "string" } }
          }
        }
        ```

*   **`analyze_middleware_and_libs_mcp`**
    *   **职责**: 检测项目使用的标准中间件和第三方库。
    *   **实现细节**: 扫描包含文件、链接库文件，识别常用中间件的特征代码
    *   **Data Schema (Output)**:
        ```jsonschema
        {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "name": { "type": "string", "enum": ["FatFs", "LwIP", "USB_Host", "USB_Device", "TouchGFX", "mbedTLS", "Ethernet"] },
              "version": { "type": "string" },
              "config_file_path": { "type": "string" },
              "detected_features": { "type": "array", "items": { "type": "string" } },
              "confidence": { "type": "number", "minimum": 0, "maximum": 1 }
            }
          }
        }
        ```

#### **类别 C：驱动与外设层分析 MCPs (精细粒度)**

*   **`analyze_uart_init_mcp`**
    *   **职责**: 提取所有 UART/USART 的初始化配置。
    *   **实现细节**: 解析HAL_UART_Init调用，提取波特率、数据位等配置参数
    *   **1.0版本实现示例**:
        ```python
        class UARTConfigExtractor:
            def __init__(self, project_path: str):
                self.project_path = Path(project_path)
                
            def extract_uart_configs(self) -> List[Dict[str, Any]]:
                """提取UART配置"""
                uart_configs = []
                
                # 查找main.c或相关初始化文件
                for c_file in self.project_path.glob("**/*main.c"):
                    with open(c_file, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                    # 使用正则表达式匹配UART初始化代码
                    uart_pattern = r'(\w+)\.Init\.BaudRate\s*=\s*(\d+);'
                    matches = re.findall(uart_pattern, content)
                    
                    for handle, baudrate in matches:
                        config = self._extract_full_uart_config(content, handle)
                        config["baudrate"] = int(baudrate)
                        uart_configs.append(config)
                        
                return uart_configs
                
            def _extract_full_uart_config(self, content: str, handle: str) -> Dict[str, Any]:
                """提取完整的UART配置"""
                config = {
                    "instance": self._extract_instance_name(content, handle),
                    "baudrate": 115200,  # 默认值
                    "data_bits": 8,
                    "stop_bits": 1,
                    "parity": "None"
                }
                
                # 提取其他配置参数
                patterns = {
                    "data_bits": rf'{handle}\.Init\.WordLength\s*=\s*UART_WORDLENGTH_(\d+)B',
                    "stop_bits": rf'{handle}\.Init\.StopBits\s*=\s*UART_STOPBITS_(\d+)',
                    "parity": rf'{handle}\.Init\.Parity\s*=\s*UART_PARITY_(\w+)'
                }
                
                for param, pattern in patterns.items():
                    match = re.search(pattern, content)
                    if match:
                        if param == "data_bits":
                            config[param] = int(match.group(1))
                        elif param == "stop_bits":
                            config[param] = int(match.group(1))
                        elif param == "parity":
                            config[param] = match.group(1).title()
                            
                return config
        ```
    *   **Data Schema (Output)**:
        ```jsonschema
        {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "instance": { "type": "string", "description": "例如: USART1" },
              "baudrate": { "type": "integer" },
              "data_bits": { "type": "integer", "enum": [8, 9] },
              "stop_bits": { "type": "number", "enum": [1, 2] },
              "parity": { "type": "string", "enum": ["None", "Even", "Odd"] },
              "flow_control": { "type": "string", "enum": ["None", "RTS", "CTS", "RTS_CTS"] },
              "mode": { "type": "string", "enum": ["TX", "RX", "TX_RX"] },
              "pins": {
                "type": "object",
                "properties": {
                  "tx_pin": { "type": "string" },
                  "rx_pin": { "type": "string" },
                  "rts_pin": { "type": "string" },
                  "cts_pin": { "type": "string" }
                }
              }
            }
          }
        }
        ```

*   **`analyze_i2c_init_mcp`**
    *   **职责**: 提取所有 I2C 的初始化配置。
    *   **实现细节**: 解析HAL_I2C_Init调用，提取时钟频率、寻址模式等配置
    *   **Data Schema (Output)**:
        ```jsonschema
        {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "instance": { "type": "string", "description": "例如: I2C1" },
              "timing_hz": { "type": "integer", "description": "例如: 100000 for 100kHz" },
              "addressing_mode": { "type": "integer", "enum": [7, 10] },
              "own_address": { "type": "integer" },
              "dual_address_mode": { "type": "boolean" },
              "general_call_mode": { "type": "boolean" },
              "no_stretch_mode": { "type": "boolean" },
              "pins": {
                "type": "object",
                "properties": {
                  "scl_pin": { "type": "string" },
                  "sda_pin": { "type": "string" }
                }
              }
            }
          }
        }
        ```

*   *... (其他外设分析MCP，如SPI, ADC, TIMER, DMA, GPIO，都将遵循类似的详细Schema定义模式) ...*

#### **类别 D：应用与逻辑层分析 MCPs**

*   **`extract_interrupt_handlers_mcp`**
    *   **职责**: 识别项目中所有的中断服务程序（ISR）。
    *   **实现细节**: 扫描中断向量表和ISR函数定义，分析中断优先级配置
    *   **1.0版本实现示例**:
        ```python
        class InterruptAnalyzer:
            def __init__(self, project_path: str):
                self.project_path = Path(project_path)
                self.isr_patterns = [
                    r'void\s+(\w+_IRQHandler)\s*\(',
                    r'NVIC_SetPriority\s*\(\s*(\w+_IRQn)\s*,\s*(\d+)\s*\)'
                ]
                
            def extract_interrupt_handlers(self) -> List[Dict[str, Any]]:
                """提取中断处理程序信息"""
                handlers = []
                
                for c_file in self.project_path.glob("**/*.c"):
                    with open(c_file, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                    # 查找中断处理程序定义
                    for pattern in self.isr_patterns:
                        matches = re.findall(pattern, content)
                        for match in matches:
                            if isinstance(match, tuple):
                                # NVIC优先级设置
                                irq_name, priority = match
                                handler_info = {
                                    "irq_name": irq_name,
                                    "priority": int(priority),
                                    "source_file": str(c_file.relative_to(self.project_path))
                                }
                            else:
                                # 中断处理程序函数
                                handler_info = {
                                    "irq_name": match,
                                    "priority": None,
                                    "source_file": str(c_file.relative_to(self.project_path)),
                                    "logic_summary": self._generate_isr_summary(content, match)
                                }
                            handlers.append(handler_info)
                            
                return handlers
                
            def _generate_isr_summary(self, content: str, isr_name: str) -> str:
                """生成ISR功能简要描述"""
                # 简单的关键字匹配来推断ISR功能
                keywords = {
                    "UART": ["HAL_UART_", "USART"],
                    "Timer": ["HAL_TIM_", "TIM"],
                    "GPIO": ["HAL_GPIO_", "EXTI"],
                    "I2C": ["HAL_I2C_"],
                    "SPI": ["HAL_SPI_"],
                    "ADC": ["HAL_ADC_"],
                    "DMA": ["HAL_DMA_"]
                }
                
                detected_functions = []
                for func_type, patterns in keywords.items():
                    for pattern in patterns:
                        if pattern in content:
                            detected_functions.append(func_type)
                            break
                            
                return f"处理 {', '.join(detected_functions)} 相关中断" if detected_functions else "通用中断处理"
        ```
    *   **Data Schema (Output)**:
        ```jsonschema
        {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "irq_name": { "type": "string", "description": "例如: USART1_IRQHandler" },
              "priority": { "type": "integer" },
              "preempt_priority": { "type": "integer" },
              "sub_priority": { "type": "integer" },
              "logic_summary": { "type": "string", "description": "LLM生成的对ISR功能的简要描述" },
              "source_file": { "type": "string" },
              "line_number": { "type": "integer" },
              "called_functions": { "type": "array", "items": { "type": "string" } }
            }
          }
        }
        ```

*   *... (其他应用逻辑分析MCP，都将遵循类似的详细Schema定义模式) ...*

---

### **阶段二：NXP 代码生成服务层 (Code Generation Service Layer)**

此阶段提供一个由多个专业的 MCP 组成的**代码生成服务集群**，负责根据**阶段一**的分析结果，生成特定领域的、符合 NXP MCUXpresso SDK 最佳实践的 C 语言代码片段。

```mermaid
graph TD
    subgraph "输入 (来自阶段一的分析结果)"
        A["Clock Tree Config"]
        B["Pin Config"]
        C["Peripheral Configs"]
        D["RTOS Config"]
    end

    subgraph "阶段二: NXP 代码生成服务集群"
        direction LR
        S1["nxp_system_codegen_mcp"]
        P1["nxp_gpio_codegen_mcp"]
        P2["nxp_uart_codegen_mcp"]
        P3["nxp_i2c_codegen_mcp"]
        P4["..."] 
        R1["nxp_rtos_codegen_mcp"]
    end

    subgraph "输出 (代码片段与配置)"
        O1["board_init.c 代码"]
        O2["外设初始化代码"]
        O3["RTOS 对象创建代码"]
    end

    A --> S1;
    B --> S1;
    C --> P1;
    C --> P2;
    C --> P3;
    D --> R1;

    S1 --> O1;
    P1 --> O2;
    P2 --> O2;
    P3 --> O2;
    R1 --> O3;
```

#### **类别 S：系统与基础层代码生成 MCPs**

*   **`nxp_system_codegen_mcp`**
    *   **职责**: 生成最基础的系统级初始化代码。
    *   **输入**: `analyze_clock_tree_mcp` 和 `analyze_pin_configuration_mcp` 的输出。
    *   **1.0版本实现示例**:
        ```python
        class NXPSystemCodeGen:
            def __init__(self):
                self.template_loader = Jinja2TemplateLoader()
                
            def generate_system_init(self, clock_config: Dict, pin_config: List[Dict]) -> Dict[str, str]:
                """生成系统初始化代码"""
                
                # 加载代码模板
                clock_template = self.template_loader.get_template('nxp_clock_init.c.j2')
                pin_template = self.template_loader.get_template('nxp_pin_init.c.j2')
                
                # 映射STM32时钟配置到NXP
                nxp_clock_config = self._map_clock_config(clock_config)
                nxp_pin_config = self._map_pin_config(pin_config)
                
                generated_files = {
                    "clock_config.c": clock_template.render(config=nxp_clock_config),
                    "pin_mux.c": pin_template.render(pins=nxp_pin_config),
                    "clock_config.h": self._generate_clock_header(nxp_clock_config),
                    "pin_mux.h": self._generate_pin_header(nxp_pin_config)
                }
                
                return generated_files
                
            def _map_clock_config(self, stm32_config: Dict) -> Dict:
                """将STM32时钟配置映射到NXP配置"""
                nxp_config = {
                    "core_clock": stm32_config.get("sysclk_hz", 120000000),
                    "bus_clock": stm32_config.get("hclk_hz", 120000000),
                    "flash_clock": stm32_config.get("hclk_hz", 120000000) // 2
                }
                
                # 根据STM32的PLL配置计算对应的NXP PLL配置
                if "pll" in stm32_config:
                    stm32_pll = stm32_config["pll"]
                    nxp_config["pll_config"] = self._calculate_nxp_pll(stm32_pll)
                    
                return nxp_config
        ```
    *   **输出 (Action)**: 生成 `BOARD_InitBootPins.c`, `BOARD_InitBootClocks.c` 等文件的完整内容。

#### **类别 P：外设驱动层代码生成 MCPs**

*   **`nxp_uart_codegen_mcp`**
    *   **职责**: 根据一个 STM32 UART 的配置，生成 NXP 平台上等效的 UART 初始化代码。
    *   **输入**: `analyze_uart_init_mcp` 的 JSON 输出。
    *   **1.0版本实现示例**:
        ```python
        class NXPUARTCodeGen:
            def __init__(self):
                self.uart_mapping = {
                    "USART1": "LPUART1",
                    "USART2": "LPUART2", 
                    "USART3": "LPUART3"
                }
                
            def generate_uart_code(self, stm32_uart_config: Dict) -> Dict[str, str]:
                """生成NXP UART初始化代码"""
                
                # 映射UART实例
                stm32_instance = stm32_uart_config["instance"]
                nxp_instance = self.uart_mapping.get(stm32_instance, "LPUART1")
                
                # 生成初始化代码
                init_code = f"""
        /* UART{nxp_instance[-1]} initialization */
        lpuart_config_t {nxp_instance.lower()}_config;
        LPUART_GetDefaultConfig(&{nxp_instance.lower()}_config);
        {nxp_instance.lower()}_config.baudRate_Bps = {stm32_uart_config["baudrate"]};
        {nxp_instance.lower()}_config.dataBitsCount = kLPUART_{'Eight' if stm32_uart_config["data_bits"] == 8 else 'Nine'}DataBits;
        {nxp_instance.lower()}_config.stopBitCount = kLPUART_{'One' if stm32_uart_config["stop_bits"] == 1 else 'Two'}StopBit;
        {nxp_instance.lower()}_config.parityMode = kLPUART_{self._map_parity(stm32_uart_config["parity"])};
        {nxp_instance.lower()}_config.enableTx = true;
        {nxp_instance.lower()}_config.enableRx = true;
        
        LPUART_Init({nxp_instance}, &{nxp_instance.lower()}_config, CLOCK_GetFreq(kCLOCK_ScgSysPllAsyncDiv2Clk));
                """
                
                header_includes = ["fsl_lpuart.h", "fsl_clock.h"]
                
                return {
                    "c_code": init_code.strip(),
                    "h_includes": header_includes,
                    "instance_mapping": {
                        "stm32": stm32_instance,
                        "nxp": nxp_instance
                    }
                }
                
            def _map_parity(self, stm32_parity: str) -> str:
                """映射奇偶校验设置"""
                parity_map = {
                    "None": "Disabled",
                    "Even": "Even", 
                    "Odd": "Odd"
                }
                return parity_map.get(stm32_parity, "Disabled")
        ```
    *   **输出示例**: `{"c_code": "uart_config_t uart_config;\nUART_GetDefaultConfig(&uart_config);\nuart_config.baudRate_Bps = 115200;\nUART_Init(UART1, &uart_config, CLOCK_GetFreq(kCLOCK_ScgSysPllAsyncDiv2Clk));", "h_includes": ["fsl_uart.h"]}`

*   **`nxp_i2c_codegen_mcp`**
    *   **职责**: 根据一个 STM32 I2C 的配置，生成 NXP 平台上等效的 I2C 初始化代码。
    *   **输入**: `analyze_i2c_init_mcp` 的 JSON 输出。
    *   **1.0版本实现示例**:
        ```python
        class NXPI2CCodeGen:
            def __init__(self):
                self.i2c_mapping = {
                    "I2C1": "LPI2C1",
                    "I2C2": "LPI2C2",
                    "I2C3": "LPI2C3"
                }
                
            def generate_i2c_code(self, stm32_i2c_config: Dict) -> Dict[str, str]:
                """生成NXP I2C初始化代码"""
                
                stm32_instance = stm32_i2c_config["instance"]
                nxp_instance = self.i2c_mapping.get(stm32_instance, "LPI2C1")
                
                init_code = f"""
        /* {nxp_instance} initialization */
        lpi2c_master_config_t {nxp_instance.lower()}_masterConfig;
        LPI2C_MasterGetDefaultConfig(&{nxp_instance.lower()}_masterConfig);
        {nxp_instance.lower()}_masterConfig.baudRate_Hz = {stm32_i2c_config["timing_hz"]};
        
        LPI2C_MasterInit({nxp_instance}, &{nxp_instance.lower()}_masterConfig, CLOCK_GetFreq(kCLOCK_ScgSysPllAsyncDiv2Clk));
                """
                
                return {
                    "c_code": init_code.strip(),
                    "h_includes": ["fsl_lpi2c.h", "fsl_clock.h"],
                    "instance_mapping": {
                        "stm32": stm32_instance,
                        "nxp": nxp_instance
                    }
                }
        ```
    *   **输出 (Action)**: 生成包含 `i2c_master_config_t` 结构体填充和 `I2C_MasterInit()` 函数调用的C代码片段及头文件引用。

*   *... (其他外设代码生成MCP，都将遵循类似的输入输出模式) ...*

#### **类别 R：RTOS 与中间件层代码生成 MCPs**

*   **`nxp_rtos_codegen_mcp`**
    *   **职责**: 根据 STM32 项目中的 RTOS 使用情况，生成 NXP 平台上等效的 RTOS 对象创建代码。
    *   **输入**: `analyze_rtos_usage_mcp` 的 JSON 输出。
    *   **1.0版本实现示例**:
        ```python
        class NXPRTOSCodeGen:
            def __init__(self):
                self.task_priority_mapping = {
                    # STM32 FreeRTOS优先级映射到NXP
                    1: 1,  # 低优先级
                    2: 2,  # 中等优先级  
                    3: 3,  # 高优先级
                }
                
            def generate_rtos_code(self, stm32_rtos_config: Dict) -> Dict[str, str]:
                """生成NXP RTOS代码"""
                
                if stm32_rtos_config["type"] != "FreeRTOS":
                    return {"c_code": "/* No RTOS detected */", "h_includes": []}
                
                code_lines = ["/* FreeRTOS task creation */"]
                includes = ["FreeRTOS.h", "task.h"]
                
                for task in stm32_rtos_config["tasks"]:
                    task_code = f"""
        xTaskCreate({task["entry_function"]},
                   "{task["name"]}", 
                   {task["stack_size_words"]},
                   NULL,
                   {self.task_priority_mapping.get(task["priority"], 1)},
                   NULL);"""
                    code_lines.append(task_code)
                
                # 如果有队列或信号量，也生成相应代码
                if "queues" in stm32_rtos_config:
                    includes.append("queue.h")
                    for queue in stm32_rtos_config["queues"]:
                        queue_code = f"""
        {queue["handle"]} = xQueueCreate({queue["length"]}, sizeof({queue["item_type"]}));"""
                        code_lines.append(queue_code)
                
                return {
                    "c_code": "\n".join(code_lines),
                    "h_includes": includes
                }
        ```
    *   **输出 (Action)**: 生成一系列 `xTaskCreate()`, `xQueueCreate()` 等 FreeRTOS API 的调用代码。

---

### **阶段三：LLM 驱动的代码生成流水线 (Generation Pipeline)**

此阶段是一个被 **Orchestrator MCP (任务编排器)** 严格管理的、自动化的、包含多个步骤的流水线。

#### **1.0版本核心实现**

```python
class LLMCodeGenerationPipeline:
    def __init__(self, llm_client, quality_gates_config):
        self.llm_client = llm_client
        self.quality_gates = QualityGatesManager(quality_gates_config)
        self.schema_validator = JSONSchemaValidator()
        
    def generate_nxp_project(self, stm32_analysis_results: Dict, target_nxp_mcu: str) -> Dict[str, Any]:
        """主要的代码生成流水线"""
        
        # 3.1 预生成：上下文准备与验证
        validation_result = self._validate_analysis_data(stm32_analysis_results)
        if not validation_result.valid:
            return {"success": False, "errors": validation_result.errors}
            
        # 3.2 生成与验证：迭代式代码生成循环
        generation_context = self._build_generation_context(stm32_analysis_results, target_nxp_mcu)
        generated_files = self._iterative_code_generation(generation_context)
        
        # 3.3 后生成 QA
        qa_results = self._run_quality_assurance(generated_files)
        
        # 3.4 交付：打包与报告  
        final_package = self._package_deliverables(generated_files, qa_results)
        
        return final_package
        
    def _validate_analysis_data(self, analysis_data: Dict) -> ValidationResult:
        """验证分析数据的完整性和正确性"""
        validation_result = ValidationResult()
        
        # Schema验证
        required_sections = ["pin_config", "clock_config", "uart_config", "build_system"]
        for section in required_sections:
            if section not in analysis_data:
                validation_result.add_error(f"Missing required section: {section}")
            else:
                # 使用JSON Schema验证每个部分
                schema_file = f"schemas/{section}_schema.json"
                if not self.schema_validator.validate(analysis_data[section], schema_file):
                    validation_result.add_error(f"Schema validation failed for {section}")
                    
        return validation_result
        
    def _build_generation_context(self, analysis_data: Dict, target_mcu: str) -> Dict[str, Any]:
        """构建LLM代码生成的上下文"""
        
        # 加载NXP MCU规格数据
        nxp_mcu_spec = self._load_nxp_mcu_spec(target_mcu)
        
        # 构建生成上下文
        context = {
            "source_platform": "STM32",
            "target_platform": "NXP",
            "target_mcu": target_mcu,
            "mcu_specifications": nxp_mcu_spec,
            "stm32_analysis": analysis_data,
            "code_generation_templates": self._load_code_templates(),
            "quality_requirements": {
                "misra_compliance": True,
                "code_coverage_target": 85,
                "max_cyclomatic_complexity": 10
            }
        }
        
        return context
        
    def _iterative_code_generation(self, context: Dict) -> Dict[str, str]:
        """迭代式代码生成"""
        generated_files = {}
        max_iterations = 3
        
        # 定义需要生成的文件列表
        file_generation_queue = [
            {"name": "main.c", "type": "application", "priority": 1},
            {"name": "board_init.c", "type": "system", "priority": 2}, 
            {"name": "peripheral_config.c", "type": "drivers", "priority": 3},
            {"name": "clock_config.c", "type": "system", "priority": 2},
            {"name": "pin_mux.c", "type": "system", "priority": 2}
        ]
        
        for file_spec in file_generation_queue:
            for iteration in range(max_iterations):
                # 构建针对特定文件的提示词
                prompt = self._build_file_specific_prompt(file_spec, context)
                
                # LLM生成代码
                generated_code = self.llm_client.generate_code(prompt)
                
                # 即时验证
                validation_result = self._validate_generated_code(generated_code, file_spec)
                
                if validation_result.success:
                    generated_files[file_spec["name"]] = generated_code
                    break
                elif iteration < max_iterations - 1:
                    # 基于验证结果改进提示词
                    context["previous_errors"] = validation_result.errors
                    context["improvement_suggestions"] = validation_result.suggestions
                else:
                    # 达到最大迭代次数，记录失败
                    generated_files[file_spec["name"]] = f"/* Generation failed after {max_iterations} iterations */"
                    
        return generated_files
        
    def _build_file_specific_prompt(self, file_spec: Dict, context: Dict) -> str:
        """构建文件特定的LLM提示词"""
        
        base_prompt = f"""
        你是一个专业的嵌入式软件工程师，需要将STM32项目迁移到NXP MCU平台。

        任务：生成{file_spec["name"]}文件的完整C代码

        源平台信息：
        - STM32分析结果：{json.dumps(context["stm32_analysis"], indent=2)}

        目标平台信息：
        - 目标MCU：{context["target_mcu"]}
        - MCU规格：{json.dumps(context["mcu_specifications"], indent=2)}

        代码要求：
        1. 严格遵循MISRA C:2012标准
        2. 使用NXP MCUXpresso SDK的API
        3. 包含完整的错误处理
        4. 添加详细的Doxygen风格注释
        5. 确保代码可以直接编译运行

        请生成完整的{file_spec["name"]}文件内容：
        """
        
        # 根据文件类型添加特定的指导信息
        if file_spec["type"] == "system":
            base_prompt += """
        
        系统文件特殊要求：
        - 包含必要的系统初始化代码
        - 正确配置时钟树
        - 设置引脚复用功能
        """
        elif file_spec["type"] == "drivers":
            base_prompt += """
        
        驱动文件特殊要求：
        - 实现外设的完整初始化
        - 包含中断处理程序
        - 提供错误恢复机制
        """
            
        return base_prompt
        
    def _validate_generated_code(self, code: str, file_spec: Dict) -> ValidationResult:
        """验证生成的代码"""
        result = ValidationResult()
        
        # 语法检查（简化版）
        if not self._basic_syntax_check(code):
            result.add_error("Code contains syntax errors")
            
        # 检查必要的包含文件
        required_includes = self._get_required_includes(file_spec["type"])
        for include in required_includes:
            if f'#include "{include}"' not in code and f'#include <{include}>' not in code:
                result.add_warning(f"Missing include: {include}")
                
        # 检查MISRA C合规性（简化版）
        misra_violations = self._check_misra_compliance(code)
        if misra_violations:
            result.add_error(f"MISRA C violations: {misra_violations}")
            
        return result
```

```mermaid
graph TD
    subgraph "3.1: 预生成"
        A["开始: 聚合分析数据"] --> B{"数据校验 (Schema)"};
        B --> C{"健康检查 (Status)"};
    end

    subgraph "3.2: 生成与验证 (迭代循环)"
        D["任务分解: 创建文件生成队列"] --> E("循环开始: 取一个文件");
        E --> F["构建提示词"];
        F --> G["LLM 生成代码"];
        G --> H{"即时静态分析"};
        H -- 修正 --> G;
        H -- 通过 --> I{"增量编译"};
        I -- 修正 --> G;
        I -- 通过 --> J("文件完成");
        J --> K{"队列是否为空?"};
        K -- 否 --> E;
        K -- 是 --> L("循环结束");
    end

    subgraph "3.3: 后生成 QA"
        M["生成单元测试"] --> N["执行单元测试"];
        N --> O["生成质量报告"];
        O --> P{"质量门禁检查"};
    end

    subgraph "3.4: 交付"
        Q["打包产物"] --> R["生成最终报告"];
    end

    C -- 通过 --> D;
    L --> M;
    P -- 通过 --> Q;
    C -- 失败 --> S["流程中止: 验证失败"];
    P -- 失败 --> T["流程中止: QA失败"];
```

#### **3.1 预生成：上下文准备与验证**
- **数据完整性验证**: 使用JSON Schema验证所有分析数据的格式和完整性
- **依赖关系检查**: 确保所有MCP服务的输出数据相互一致
- **目标平台兼容性**: 验证选定的NXP MCU能够支持所有STM32功能

#### **3.2 生成与验证：迭代式代码生成循环**
*   **提示词构建 (Prompt Engineering) 的增强**: Orchestrator 先调用**阶段二**中对应的 `nxp_*_codegen_mcp`，将返回的、保证正确的 NXP 初始化代码片段直接注入到提示词中，作为 LLM 必须使用的"基础代码块"。
*   **质量门禁集成**: 每个生成的代码片段都必须通过编译检查、静态分析、MISRA C规则检查
*   **增量编译验证**: 实时验证生成代码的可编译性，及时发现和修正问题

#### **3.3 后生成：自动化测试与质量评估**
- **单元测试生成**: 为关键函数自动生成测试用例
- **代码覆盖率分析**: 确保测试覆盖率达到设定标准（85%）
- **性能基准测试**: 对比STM32和NXP版本的性能指标

#### **3.4 交付：打包与报告**
- **完整项目包**: 包含所有源文件、构建脚本、配置文件
- **迁移报告**: 详细说明迁移过程、修改内容、注意事项
- **质量报告**: 代码质量指标、测试结果、合规性检查结果

---

## **1.0版本Demo具体实现细节**

### **JSON Schema验证框架**

```python
class MCPSchemaValidator:
    def __init__(self):
        self.schemas = self._load_schemas()
        
    def _load_schemas(self) -> Dict[str, Any]:
        """加载所有MCP输出的JSON Schema"""
        schemas = {}
        schema_dir = Path("schemas")
        
        for schema_file in schema_dir.glob("*.json"):
            with open(schema_file, 'r') as f:
                schema_name = schema_file.stem
                schemas[schema_name] = json.load(f)
                
        return schemas
        
    def validate_mcp_output(self, mcp_name: str, output_data: Any) -> ValidationResult:
        """验证MCP输出数据"""
        if mcp_name not in self.schemas:
            return ValidationResult(False, [f"No schema found for {mcp_name}"])
            
        try:
            jsonschema.validate(output_data, self.schemas[mcp_name])
            return ValidationResult(True, [])
        except jsonschema.ValidationError as e:
            return ValidationResult(False, [str(e)])
```

### **错误处理与恢复机制**

```python
class ErrorRecoveryManager:
    def __init__(self):
        self.error_handlers = {
            "E_ANALYSIS_INCOMPLETE": self._handle_analysis_incomplete,
            "E_SCHEMA_VALIDATION_FAILED": self._handle_schema_validation_failed,
            "E_CODEGEN_RETRY_LIMIT": self._handle_codegen_retry_limit,
            "E_COMPILE_RETRY_LIMIT": self._handle_compile_retry_limit,
            "E_QA_COVERAGE_LOW": self._handle_qa_coverage_low
        }
        
    def handle_error(self, error_code: str, context: Dict) -> RecoveryAction:
        """处理错误并返回恢复建议"""
        if error_code in self.error_handlers:
            return self.error_handlers[error_code](context)
        else:
            return RecoveryAction("ABORT", "Unknown error code")
            
    def _handle_analysis_incomplete(self, context: Dict) -> RecoveryAction:
        """处理分析不完整错误"""
        missing_components = context.get("missing_components", [])
        
        if len(missing_components) <= 2:
            return RecoveryAction("RETRY", f"Retry analysis for {missing_components}")
        else:
            return RecoveryAction("MANUAL_REVIEW", "Too many missing components, requires manual review")
```

---

## 附录：工程技术实现规范

### A.1 统一 MCP 接口规范 (I/O 标准化)

**1. 标准输入 (针对分析型 MCP):**
```json
{
  "project_path": "/path/to/stm32_project_root",
  "analysis_config": {
    "deep_analysis": true,
    "include_generated_code": false,
    "target_nxp_family": "MCXN"
  }
}
```

**2. 标准输出 (信封协议):**
```json
{
  "mcp_name": "mcp_service_name",
  "status": "success" | "partial_success" | "error" | "needs_human_review",
  "version": "1.0.0",
  "timestamp": "2025-01-08T10:30:00Z",
  "execution_time_ms": 1250,
  "data": { /* MCP 特有的、遵循预定义 Schema 的数据 */ },
  "metadata": {
    "confidence_score": 0.95,
    "data_quality": "high",
    "validation_passed": true
  },
  "error": {
    "code": "E_ERROR_CODE", 
    "message": "给人类阅读的详细错误信息",
    "suggestion": "给程序看的建议，如 'retry_with_simplified_logic'",
    "recovery_actions": ["action1", "action2"]
  }
}
```

### A.2 Orchestrator (任务编排器) 规范

*   **状态机**: Orchestrator 必须实现一个明确的状态机，状态包括：`IDLE`, `ANALYZING`, `VALIDATING_CONTEXT`, `GENERATING`, `STATIC_ANALYZING`, `COMPILING`, `UNIT_TESTING`, `ASSESSING_QUALITY`, `PACKAGING`, `DONE`, `ERROR`, `PAUSED_FOR_REVIEW`。
*   **日志**: Orchestrator 必须为每一次完整的运行生成详细的、带时间戳的日志文件。
*   **依赖管理**: Orchestrator 负责读取一个任务依赖图，按正确的顺序调用各个分析型 MCP。
    *   **依赖图示例 (`dependency_graph.json`)**:
        ```json
        {
          "analyze_uart_init_mcp": ["analyze_clock_tree_mcp", "analyze_pin_configuration_mcp"],
          "analyze_timer_init_mcp": ["analyze_clock_tree_mcp"],
          "nxp_uart_codegen_mcp": ["analyze_uart_init_mcp", "find_nxp_equivalent_mcp"],
          "llm_generation_pipeline": ["nxp_uart_codegen_mcp", "nxp_system_codegen_mcp"]
        }
        ```

### A.3 分析与抽象 MCP 的接口映射原则

**核心原则**: 分析 MCP 的输出，就是代码生成 MCP 的输入。

**1.0版本映射示例**:
```python
class MCPInterfaceMapper:
    def __init__(self):
        self.mapping_rules = {
            "analyze_uart_init_mcp": {
                "output_schema": "uart_analysis_schema.json",
                "consumer_mcps": ["nxp_uart_codegen_mcp"],
                "data_transformations": ["normalize_baudrate", "map_pin_names"]
            }
        }
        
    def validate_interface_compatibility(self, producer_mcp: str, consumer_mcp: str) -> bool:
        """验证MCP接口兼容性"""
        if producer_mcp not in self.mapping_rules:
            return False
            
        producer_config = self.mapping_rules[producer_mcp]
        return consumer_mcp in producer_config["consumer_mcps"]
```

### A.4 标准错误码与处理策略

*   **错误码表**: 必须维护一个集中的错误码文档。
    *   `E_ANALYSIS_INCOMPLETE`: 分析阶段有 MCP 失败。
    *   `E_SCHEMA_VALIDATION_FAILED`: MCP 输出不符合 Schema。
    *   `E_CODEGEN_RETRY_LIMIT`: LLM 自动修正代码达到次数上限。
    *   `E_COMPILE_RETRY_LIMIT`: 编译失败达到次数上限。
    *   `E_QA_COVERAGE_LOW`: 测试覆盖率低于门禁标准。
    *   `E_IOC_PARSE_ERROR`: .ioc文件解析失败。
    *   `E_NXP_MCU_NOT_FOUND`: 找不到匹配的NXP MCU。
    *   `E_PIN_MAPPING_CONFLICT`: 引脚映射冲突。

### A.5 代码生成与质量规范

*   **编码标准**: 所有生成的 C 代码必须严格遵守 **MISRA C:2012** 规范。
*   **文件头模板**: 每个生成的 `.c` 和 `.h` 文件都必须包含一个标准的文件头。
    ```c
    /**
     * @file    ${filename}
     * @brief   ${brief_description}
     * <AUTHOR> Migration Tool v1.0
     * @date    ${generation_date}
     * @note    Generated from STM32 project: ${source_project_name}
     *          Target NXP MCU: ${target_mcu}
     * 
     * @warning This file is auto-generated. Manual modifications may be lost
     *          during regeneration. Use USER CODE sections for custom code.
     */
    ```
*   **注释策略**: 所有函数声明必须有 Doxygen 风格的注释。

### A.6 验证与测试规范

*   **测试框架**: 统一使用 **Ceedling** (集成了 Unity, CMock) 作为单元测试框架。
*   **质量门禁 (Quality Gate)**: 定义明确的通过标准，写入 Orchestrator 的配置文件中。
    *   `min_line_coverage`: 85%
    *   `min_branch_coverage`: 75%
    *   `max_cyclomatic_complexity`: 10
    *   `static_analysis_errors`: 0
    *   `compilation_warnings`: 0
    *   `misra_c_violations`: 0

### A.7 版本控制规范

*   **MCP 版本**: 所有 MCP 服务自身必须遵循 **SemVer 2.0.0** (语义化版本) 进行版本管理。
*   **生成产物版本**: 最终生成的代码包应有自己的版本号，可采用 `YYYYMMDD-HHMMSS` 的时间戳格式。
*   **配置文件版本**: 所有JSON Schema和配置文件都应包含版本信息，确保向前兼容性。

### A.8 1.0版本交付清单

**必须交付的组件**:
1. **核心MCP服务** (至少5个分析MCP + 3个代码生成MCP)
2. **JSON Schema验证框架** (完整的schema定义和验证逻辑)
3. **基础LLM集成** (支持至少一个主流LLM API)
4. **简单的Web UI** (用于项目上传和结果查看)
5. **示例STM32项目** (用于演示和测试)
6. **完整的测试套件** (单元测试 + 集成测试)
7. **部署文档** (安装、配置、使用说明)

**可选交付的组件**:
1. **高级错误恢复机制**
2. **批量处理支持**
3. **性能优化模块**
4. **扩展的NXP MCU支持**
