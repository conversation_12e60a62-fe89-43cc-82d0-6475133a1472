# VSCode MCP集成指南

本文档介绍如何在VSCode中集成STM32到NXP MCU代码迁移工具的MCP服务。

## 前提条件

1. **安装VSCode**：确保已安装最新版本的Visual Studio Code
2. **安装MCP扩展**：在VSCode扩展市场中搜索并安装支持MCP协议的扩展
3. **Python环境**：确保已安装Python 3.8+并配置好项目依赖

## 配置步骤

### 1. 配置MCP服务器

在VSCode的设置中添加MCP服务器配置。有两种方式：

#### 方式一：通过设置界面
1. 打开VSCode设置 (Ctrl+,)
2. 搜索 "mcp"
3. 找到 "MCP: Servers" 设置
4. 点击 "Edit in settings.json"

#### 方式二：直接编辑settings.json
打开VSCode的settings.json文件，添加以下配置：

```json
{
  "mcp.servers": {
    "stm32-to-nxp-migration": {
      "command": "python",
      "args": [
        "src/interfaces/mcp/server.py"
      ],
      "cwd": "/path/to/stm32-to-nxp-migration-tool",
      "env": {
        "PYTHONPATH": "src"
      }
    }
  }
}
```

**注意**：请将 `/path/to/stm32-to-nxp-migration-tool` 替换为实际的项目路径。

### 2. 启动MCP服务

配置完成后，重启VSCode或重新加载窗口。MCP服务器将自动启动。

### 3. 验证连接

1. 打开VSCode的命令面板 (Ctrl+Shift+P)
2. 输入 "MCP" 查看可用的MCP命令
3. 选择 "MCP: List Tools" 查看可用工具

## 可用工具

### 1. analyze_stm32_pins
**描述**：分析STM32项目的引脚配置

**使用方法**：
1. 在命令面板中输入 "MCP: Call Tool"
2. 选择 "analyze_stm32_pins"
3. 输入参数：
   ```json
   {
     "project_path": "/path/to/stm32_project",
     "deep_analysis": true
   }
   ```

**输出示例**：
```
# STM32引脚配置分析结果

**项目信息:**
- 总引脚数: 15
- MCU封装: LQFP100
- 配置文件: sample_project.ioc

**引脚使用统计:**
- GPIO引脚: 6
- 复用功能引脚: 4
- 模拟引脚: 1

**外设使用情况:**
- USART1 (UART): 2 个引脚
  引脚: PA9, PA10
- USART2 (UART): 2 个引脚
  引脚: PA2, PA3
```

### 2. generate_nxp_uart
**描述**：生成NXP UART初始化代码

**使用方法**：
1. 在命令面板中输入 "MCP: Call Tool"
2. 选择 "generate_nxp_uart"
3. 输入参数：
   ```json
   {
     "uart_configurations": [
       {
         "instance": "USART1",
         "baudrate": 115200,
         "data_bits": 8,
         "stop_bits": 1,
         "parity": "None"
       }
     ],
     "target_mcu": "MCXN947"
   }
   ```

**输出示例**：
```c
/**
 * @file    lpuart1_config.c
 * @brief   LPUART1 configuration and initialization
 */

#include "lpuart1_config.h"
#include "fsl_lpuart.h"
#include "fsl_clock.h"

status_t LPUART1_Init(void)
{
    lpuart_config_t lpuart1_config;
    
    LPUART_GetDefaultConfig(&lpuart1_config);
    lpuart1_config.baudRate_Bps = 115200;
    lpuart1_config.dataBitsCount = kLPUART_EightDataBits;
    // ... 更多代码
}
```

### 3. list_mcp_services
**描述**：列出所有可用的MCP服务

**使用方法**：
1. 在命令面板中输入 "MCP: Call Tool"
2. 选择 "list_mcp_services"
3. 无需输入参数

## 工作流程示例

### 完整的项目迁移流程

1. **分析STM32项目**
   ```
   使用工具: analyze_stm32_pins
   输入: STM32项目路径
   输出: 引脚配置分析报告
   ```

2. **生成NXP代码**
   ```
   使用工具: generate_nxp_uart
   输入: UART配置信息
   输出: NXP UART初始化代码
   ```

3. **集成到项目**
   - 将生成的代码复制到NXP项目中
   - 根据分析报告调整引脚映射
   - 编译和测试

## 故障排除

### 常见问题

1. **MCP服务器启动失败**
   - 检查Python路径是否正确
   - 确保已安装所有依赖包
   - 查看VSCode输出面板中的错误信息

2. **工具调用失败**
   - 检查输入参数格式是否正确
   - 确保项目路径存在且可访问
   - 查看MCP服务器日志文件

3. **生成的代码有问题**
   - 检查输入的配置信息是否完整
   - 确认目标MCU型号是否支持
   - 查看代码质量指标

### 调试方法

1. **启用详细日志**
   在环境变量中设置：
   ```bash
   export PYTHONPATH=src
   export LOG_LEVEL=DEBUG
   ```

2. **查看日志文件**
   MCP服务器会在项目根目录生成 `mcp_server.log` 文件

3. **手动测试服务**
   可以直接运行MCP服务器进行测试：
   ```bash
   python src/interfaces/mcp/server.py
   ```

## 高级配置

### 自定义工具

可以通过修改 `src/interfaces/mcp/server.py` 文件来添加新的工具：

```python
def _register_tools(self):
    self.tools["my_custom_tool"] = {
        "description": "我的自定义工具",
        "inputSchema": {
            "type": "object",
            "properties": {
                "param1": {"type": "string"}
            }
        }
    }
```

### 性能优化

1. **缓存机制**：启用分析结果缓存
2. **并发处理**：配置多个MCP服务器实例
3. **资源限制**：设置内存和CPU使用限制

## 最佳实践

1. **项目组织**
   - 将STM32项目和NXP项目分别放在不同目录
   - 使用版本控制跟踪迁移过程

2. **配置管理**
   - 将常用的配置保存为JSON文件
   - 使用模板简化重复配置

3. **质量控制**
   - 始终检查生成代码的质量指标
   - 进行编译测试验证
   - 使用静态分析工具

## 支持和反馈

如果在使用过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查项目的GitHub Issues
3. 提交新的Issue并提供详细信息

---

**注意**：本集成需要VSCode支持MCP协议。如果您的VSCode版本不支持，请升级到最新版本或安装相应的扩展。
